#!/usr/bin/env python3
"""检查两个数据库的内容"""

import sqlite3
import os
from pathlib import Path

def check_database(db_path):
    """检查数据库内容"""
    print(f"\n=== 检查数据库: {db_path} ===")
    
    if not os.path.exists(db_path):
        print("数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"表数量: {len(tables)}")
        
        for table in tables:
            table_name = table[0]
            print(f"\n表: {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"  列: {[col[1] for col in columns]}")
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  记录数: {count}")
            
            # 如果是datasets表，显示一些数据
            if table_name == 'datasets' and count > 0:
                cursor.execute(f"SELECT id, name, path FROM {table_name} LIMIT 5")
                records = cursor.fetchall()
                print("  示例数据:")
                for record in records:
                    print(f"    ID: {record[0]}, Name: {record[1]}, Path: {record[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")

def main():
    # 检查两个数据库
    db1 = r"D:\anylabeling2\anylabeling\utils\AI_ENV\configs\anylabeling.db"
    db2 = r"D:\anylabeling2\AI_ENV\configs\anylabeling.db"
    
    check_database(db1)
    check_database(db2)

if __name__ == "__main__":
    main()
