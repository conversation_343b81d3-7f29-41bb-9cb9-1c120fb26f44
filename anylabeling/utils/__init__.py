"""
工具模块
"""

# 导入原有的工具类
import hashlib
import os
import sys

# 尝试导入PyQt5，如果失败则跳过GUI相关功能
try:
    from PyQt5.QtCore import QObject, pyqtSignal, QThread
    HAS_PYQT5 = True
except ImportError:
    HAS_PYQT5 = False
    # 创建占位符类，避免导入错误
    class QObject:
        pass
    class QThread:
        pass
    def pyqtSignal(*args, **kwargs):
        return None

# 原有的工具函数和类
def natural_sort(text, _nsre=None):
    """自然排序函数"""
    import re
    if _nsre is None:
        _nsre = re.compile(r'(\d+)')
    return [int(text) if text.isdigit() else text.lower() for text in _nsre.split(text)]

def distance(p):
    """计算距离"""
    return ((p[0] - p[1]) ** 2 + (p[2] - p[3]) ** 2) ** 0.5

def format_shortcut(text):
    """格式化快捷键"""
    mod, key = text.split('+', 1)
    return '<b>%s</b>+<b>%s</b>' % (mod, key)

def generate_color_by_text(text):
    """根据文本生成颜色"""
    s = hashlib.md5(text.encode()).hexdigest()
    return tuple(int(s[i:i+2], 16) for i in (0, 2, 4))

def have_qstring():
    """检查是否有QString"""
    return True

def util_qt_strlistclass():
    """获取字符串列表类"""
    return list

if HAS_PYQT5:
    class GenericWorker(QObject):
        """通用工作线程"""
        finished = pyqtSignal()

        def __init__(self, func, *args, **kwargs):
            super().__init__()
            self.func = func
            self.args = args
            self.kwargs = kwargs

        def run(self):
            """运行函数"""
            self.func(*self.args, **self.kwargs)
            self.finished.emit()
else:
    class GenericWorker:
        """通用工作线程（无PyQt5版本）"""
        def __init__(self, func, *args, **kwargs):
            self.func = func
            self.args = args
            self.kwargs = kwargs

        def run(self):
            """运行函数"""
            self.func(*self.args, **self.kwargs)

# 导入新增的AI训练相关模块
from .db_manager import DatabaseManager

__all__ = [
    # 原有工具函数
    'natural_sort',
    'distance', 
    'format_shortcut',
    'generate_color_by_text',
    'have_qstring',
    'util_qt_strlistclass',
    'GenericWorker',
    # 新增AI训练模块
    'DatabaseManager',
]
