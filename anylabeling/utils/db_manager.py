"""
统一的数据库管理器
合并了原来的三个数据库管理器：
- utils/db_manager.py - 训练数据库管理器
- anylabeling/services/database/database_manager.py - 应用服务数据库管理器  
- anylabeling/utils/db_manager.py - anylabeling包的数据库管理器

负责管理任务、数据集、模型和标注信息的数据存储
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from contextlib import contextmanager
from pathlib import Path


class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_path: str = None):
        if db_path is None:
            # 获取项目根目录的绝对路径
            project_root = Path(__file__).parent.parent.parent
            db_path = project_root / "AI_ENV" / "configs" / "anylabeling.db"
        self.db_path = str(db_path)
        self._ensure_db_dir()
        self._init_database()
    
    def _ensure_db_dir(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
    
    def _init_database(self):
        """初始化数据库表结构"""
        with self._get_connection() as conn:
            cursor = conn.cursor()

            # 1. 标注文件表 (每个数据集独立的标注记录)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS annotations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    dataset_id INTEGER NOT NULL,
                    image_path TEXT NOT NULL,
                    label_path TEXT,
                    image_md5 TEXT,
                    status TEXT NOT NULL DEFAULT 'unlabeled',
                    width INTEGER,
                    height INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (dataset_id) REFERENCES datasets(id) ON DELETE CASCADE,
                    UNIQUE(dataset_id, image_path)  -- 确保数据集隔离
                )
            ''')

            # 2. 数据集表（统一表结构）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS datasets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    type TEXT NOT NULL,
                    path TEXT NOT NULL,
                    output_dir TEXT,
                    description TEXT,
                    total_images INTEGER DEFAULT 0,
                    annotated_images INTEGER DEFAULT 0,
                    classes_json TEXT,
                    statistics_json TEXT,
                    created_time TEXT NOT NULL,
                    updated_time TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 3. 数据集-标注关联表 (多对多)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dataset_annotations_link (
                    dataset_id INTEGER NOT NULL,
                    annotation_id INTEGER NOT NULL,
                    role TEXT, -- e.g., 'train', 'val', 'test'
                    PRIMARY KEY (dataset_id, annotation_id),
                    FOREIGN KEY (dataset_id) REFERENCES datasets (id) ON DELETE CASCADE,
                    FOREIGN KEY (annotation_id) REFERENCES annotations (id) ON DELETE CASCADE
                )
            ''')

            # 4. 任务表 (保持不变)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    task_type TEXT NOT NULL,
                    python_env_path TEXT NOT NULL,
                    train_script_path TEXT NOT NULL,
                    test_script_path TEXT NOT NULL,
                    output_dir TEXT NOT NULL,
                    description TEXT,
                    train_params_definition TEXT,
                    test_params_definition TEXT,
                    created_time TEXT NOT NULL,
                    updated_time TEXT NOT NULL
                )
            ''')

            # 5. 模型表 (保持不变)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS models (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    task_id INTEGER NOT NULL,
                    dataset_id INTEGER, -- This now links to the new datasets table
                    status TEXT NOT NULL DEFAULT 'created',
                    train_params TEXT,
                    test_params TEXT,
                    onnx_path TEXT,
                    tensorrt_path TEXT,
                    config_path TEXT,
                    metrics TEXT,
                    train_start_time TEXT,
                    train_end_time TEXT,
                    test_time TEXT,
                    created_time TEXT NOT NULL,
                    updated_time TEXT NOT NULL,
                    FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE,
                    FOREIGN KEY (dataset_id) REFERENCES datasets (id) ON DELETE SET NULL
                )
            ''')

            conn.commit()
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        # 启用外键约束
        conn.execute('PRAGMA foreign_keys = ON')
        try:
            yield conn
        finally:
            conn.close()
    
    def _dict_from_row(self, row) -> Dict[str, Any]:
        """将数据库行转换为字典"""
        return dict(row) if row else {}
    
    def _current_time(self) -> str:
        """获取当前时间字符串"""
        return datetime.now().isoformat()

    # ==================== 标注文件管理 (Annotations) ====================

    def add_annotation(self, annotation_data: Dict[str, Any]) -> int:
        """添加一个标注文件记录（每个数据集独立）"""
        current_time = self._current_time()
        dataset_id = annotation_data.get('dataset_id')
        if not dataset_id:
            raise ValueError("dataset_id is required for annotation")
            
        with self._get_connection() as conn:
            cursor = conn.cursor()
            # 检查同一数据集中是否已存在相同路径的记录
            cursor.execute(
                "SELECT * FROM annotations WHERE dataset_id = ? AND image_path = ?",
                (dataset_id, annotation_data['image_path'])
            )
            existing = cursor.fetchone()
            if existing:
                # 更新现有记录
                existing_dict = self._dict_from_row(existing)
                cursor.execute('''
                    UPDATE annotations SET
                        label_path = ?, image_md5 = ?, width = ?, height = ?, status = ?, updated_at = ?
                    WHERE id = ?
                ''', (
                    annotation_data.get('label_path'),
                    annotation_data.get('md5'),
                    annotation_data.get('width'),
                    annotation_data.get('height'),
                    annotation_data.get('status', 'unlabeled'),
                    current_time,
                    existing_dict['id']
                ))
                conn.commit()
                return existing_dict['id']
            else:
                # 插入新记录
                cursor.execute('''
                    INSERT INTO annotations (
                        dataset_id, image_path, label_path, image_md5, width, height, status, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    dataset_id,
                    annotation_data['image_path'],
                    annotation_data.get('label_path'),
                    annotation_data.get('md5'),
                    annotation_data.get('width'),
                    annotation_data.get('height'),
                    annotation_data.get('status', 'unlabeled'),
                    current_time,
                    current_time
                ))
                conn.commit()
                return cursor.lastrowid

    def get_annotation_by_path(self, image_path: str) -> Optional[Dict[str, Any]]:
        """通过图片路径获取标注记录"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM annotations WHERE image_path = ?", (image_path,))
            return self._dict_from_row(cursor.fetchone())

    def get_annotation_by_md5(self, md5_hash: str) -> Optional[Dict[str, Any]]:
        """通过MD5获取标注记录"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM annotations WHERE image_md5 = ?", (md5_hash,))
            return self._dict_from_row(cursor.fetchone())

    def update_annotation_status(self, annotation_id: int, status: str, label_path: Optional[str] = None) -> bool:
        """更新标注状态和标签路径"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            if label_path:
                cursor.execute(
                    "UPDATE annotations SET status = ?, label_path = ?, updated_time = ? WHERE id = ?",
                    (status, label_path, self._current_time(), annotation_id)
                )
            else:
                cursor.execute(
                    "UPDATE annotations SET status = ?, updated_time = ? WHERE id = ?",
                    (status, self._current_time(), annotation_id)
                )
            conn.commit()
            return cursor.rowcount > 0

    def get_dataset_id_by_annotation_path(self, image_path: str) -> Optional[int]:
        """根据图片路径获取所属的数据集ID"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT dal.dataset_id FROM dataset_annotations_link dal
                JOIN annotations a ON dal.annotation_id = a.id
                WHERE a.image_path = ?
            ''', (image_path,))
            result = cursor.fetchone()
            return result['dataset_id'] if result else None

    # ==================== 数据集管理 (Datasets) ====================
    
    def create_dataset(self, name: str, output_dir: str, description: str = '') -> int:
        """创建新数据集"""
        current_time = self._current_time()
        with self._get_connection() as conn:
            cursor = conn.cursor()
            # 使用统一的数据库表结构，包含所有必要字段
            cursor.execute('''
                INSERT INTO datasets (
                    name, type, path, output_dir, description,
                    total_images, annotated_images, classes_json, statistics_json,
                    created_time, updated_time, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                name, 
                'mixed',  # 默认类型
                output_dir,  # path字段使用output_dir的值
                output_dir,  # output_dir字段
                description,
                0,  # total_images
                0,  # annotated_images
                '[]',  # classes_json
                '{}',  # statistics_json
                current_time,  # created_time
                current_time,  # updated_time
                current_time   # created_at
            ))
            conn.commit()
            return cursor.lastrowid

    def get_all_datasets(self) -> List[Dict[str, Any]]:
        """获取所有数据集"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM datasets ORDER BY created_time DESC')
            return [self._dict_from_row(row) for row in cursor.fetchall()]

    def get_dataset(self, dataset_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取数据集"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM datasets WHERE id = ?', (dataset_id,))
            row = cursor.fetchone()
            return self._dict_from_row(row)

    def delete_dataset(self, dataset_id: int) -> bool:
        """删除数据集（会自动删除link表中的关联）"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM datasets WHERE id = ?', (dataset_id,))
            conn.commit()
            return cursor.rowcount > 0

    def update_dataset_stats(self, dataset_id: int, update_data: Dict[str, Any]) -> bool:
        """更新数据集统计信息"""
        if not update_data:
            return False

        # 构建SET子句
        set_clauses = []
        values = []

        for key, value in update_data.items():
            if key in ['image_count', 'label_count', 'processed_path', 'status']:
                set_clauses.append(f"{key} = ?")
                values.append(value)

        if not set_clauses:
            return False

        # 添加更新时间
        set_clauses.append("updated_time = ?")
        values.append(self._current_time())
        values.append(dataset_id)

        with self._get_connection() as conn:
            cursor = conn.cursor()
            sql = f"UPDATE datasets SET {', '.join(set_clauses)} WHERE id = ?"
            cursor.execute(sql, values)
            conn.commit()
            return cursor.rowcount > 0

    def link_annotation_to_dataset(self, dataset_id: int, annotation_id: int):
        """将标注文件关联到数据集"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR IGNORE INTO dataset_annotations_link (dataset_id, annotation_id)
                VALUES (?, ?)
            ''', (dataset_id, annotation_id))
            conn.commit()

    def get_dataset_stats(self, dataset_id: int) -> Dict[str, int]:
        """获取数据集的统计信息"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # 图片总数（直接从annotations表查询）
            cursor.execute(
                "SELECT COUNT(*) FROM annotations WHERE dataset_id = ?",
                (dataset_id,)
            )
            total_count = cursor.fetchone()[0]
            
            # 已标注数
            cursor.execute('''
                SELECT COUNT(*) FROM annotations 
                WHERE dataset_id = ? AND status = 'labeled'
            ''', (dataset_id,))
            labeled_count = cursor.fetchone()[0]
            
            # 无标注数
            cursor.execute('''
                SELECT COUNT(*) FROM annotations 
                WHERE dataset_id = ? AND status IN ('unlabeled', 'empty_labeled')
            ''', (dataset_id,))
            unlabeled_count = cursor.fetchone()[0]
            
            return {
                'total_images': total_count,
                'labeled_images': labeled_count,
                'unlabeled_images': unlabeled_count
            }

    def get_dataset_label_stats(self, dataset_id: int) -> Dict[str, int]:
        """获取数据集的标签统计信息"""
        import json
        import os
        
        label_stats = {}
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取数据集中所有有标注的文件
            cursor.execute('''
                SELECT a.label_path FROM annotations a
                JOIN dataset_annotations_link dal ON a.id = dal.annotation_id
                WHERE dal.dataset_id = ? AND a.status = 'labeled' AND a.label_path IS NOT NULL
            ''', (dataset_id,))
            
            label_files = cursor.fetchall()
            
            for (label_path,) in label_files:
                if label_path and os.path.exists(label_path):
                    try:
                        with open(label_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            shapes = data.get('shapes', [])
                            
                            for shape in shapes:
                                label = shape.get('label', 'unknown')
                                label_stats[label] = label_stats.get(label, 0) + 1
                                
                    except Exception:
                        continue
        
        return label_stats

    def get_annotations_for_dataset(self, dataset_id: int) -> List[Dict[str, Any]]:
        """获取一个数据集下的所有标注信息（直接查询）"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM annotations WHERE dataset_id = ?
            ''', (dataset_id,))
            return [self._dict_from_row(row) for row in cursor.fetchall()]

    def sync_annotations_status(self, dataset_id: int, dataset_dir: str) -> Dict[str, int]:
        """同步数据集标注状态的简化版本"""
        import json
        
        stats = {'updated': 0, 'added': 0, 'errors': 0}
        
        if not os.path.exists(dataset_dir):
            return stats
        
        # 获取该数据集的所有标注记录
        annotations = self.get_annotations_for_dataset(dataset_id)
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            current_time = self._current_time()
            
            for annotation in annotations:
                try:
                    image_path = annotation['image_path']
                    annotation_id = annotation['id']
                    
                    # 查找对应的JSON标注文件
                    # 首先尝试同目录下的JSON文件
                    json_path = os.path.splitext(image_path)[0] + '.json'
                    
                    # 如果同目录下没有，尝试在labels子目录中查找
                    if not os.path.exists(json_path):
                        # 获取图片文件名（不含扩展名）
                        image_basename = os.path.splitext(os.path.basename(image_path))[0]
                        # 在数据集目录的labels子目录中查找
                        labels_dir = os.path.join(dataset_dir, 'labels')
                        if os.path.exists(labels_dir):
                            json_path = os.path.join(labels_dir, image_basename + '.json')
                    
                    label_path = json_path if os.path.exists(json_path) else None
                    
                    # 判断标注状态
                    status = 'unlabeled'
                    if label_path and os.path.exists(label_path):
                        try:
                            with open(label_path, 'r', encoding='utf-8') as f:
                                json_data = json.load(f)
                                # 检查是否有标注内容
                                shapes = json_data.get('shapes', [])
                                if shapes and len(shapes) > 0:
                                    # 检查是否有有效的标注
                                    has_valid_annotation = any(
                                        shape.get('points') and len(shape.get('points', [])) > 0
                                        for shape in shapes
                                    )
                                    status = 'labeled' if has_valid_annotation else 'empty_labeled'
                                else:
                                    status = 'empty_labeled'
                        except Exception:
                            status = 'unlabeled'
                    
                    # 只有当状态发生变化时才更新
                    if annotation['status'] != status or annotation['label_path'] != label_path:
                        cursor.execute('''
                            UPDATE annotations SET
                                label_path = ?, status = ?, updated_time = ?
                            WHERE id = ?
                        ''', (label_path, status, current_time, annotation_id))
                        stats['updated'] += 1
                        
                except Exception as e:
                    print(f"处理标注记录 {annotation.get('image_path', 'unknown')} 时出错: {e}")
                    stats['errors'] += 1
                    continue
            
            conn.commit()
        
        return stats



    def sync_dataset_annotations(self, dataset_id: int, dataset_dir: str) -> Dict[str, int]:
        """同步数据集的标注状态
        
        Args:
            dataset_id: 数据集ID
            dataset_dir: 数据集目录路径
            
        Returns:
            Dict包含同步统计信息: {'updated': count, 'added': count, 'errors': count}
        """
        import glob
        import json
        from PIL import Image
        import hashlib
        
        stats = {'updated': 0, 'added': 0, 'errors': 0}
        
        if not os.path.exists(dataset_dir):
            return stats
            
        # 支持的图片格式
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        
        # 扫描目录中的所有图片文件
        image_files = []
        for ext in image_extensions:
            # 只递归扫描子目录（这样会包含根目录的文件）
            image_files.extend(glob.glob(os.path.join(dataset_dir, '**', f'*{ext}'), recursive=True))
            image_files.extend(glob.glob(os.path.join(dataset_dir, '**', f'*{ext.upper()}'), recursive=True))
        
        # 去重（防止重复文件）
        image_files = list(set(image_files))
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            for image_path in image_files:
                try:
                    # 计算图片的基本信息
                    image_path = os.path.abspath(image_path)
                    
                    # 计算MD5
                    with open(image_path, 'rb') as f:
                        md5_hash = hashlib.md5(f.read()).hexdigest()
                    
                    # 获取图片尺寸
                    try:
                        with Image.open(image_path) as img:
                            width, height = img.size
                    except Exception:
                        width, height = None, None
                    
                    # 查找对应的JSON标注文件
                    # 首先尝试同目录下的JSON文件
                    json_path = os.path.splitext(image_path)[0] + '.json'
                    
                    # 如果同目录下没有，尝试在labels子目录中查找
                    if not os.path.exists(json_path):
                        # 获取图片文件名（不含扩展名）
                        image_basename = os.path.splitext(os.path.basename(image_path))[0]
                        # 在数据集目录的labels子目录中查找
                        labels_dir = os.path.join(dataset_dir, 'labels')
                        if os.path.exists(labels_dir):
                            json_path = os.path.join(labels_dir, image_basename + '.json')
                    
                    label_path = json_path if os.path.exists(json_path) else None
                    
                    # 判断标注状态
                    status = 'unlabeled'
                    if label_path and os.path.exists(label_path):
                        try:
                            with open(label_path, 'r', encoding='utf-8') as f:
                                json_data = json.load(f)
                                # 检查是否有标注内容
                                shapes = json_data.get('shapes', [])
                                if shapes and len(shapes) > 0:
                                    # 检查是否有有效的标注
                                    has_valid_annotation = any(
                                        shape.get('points') and len(shape.get('points', [])) > 0
                                        for shape in shapes
                                    )
                                    status = 'labeled' if has_valid_annotation else 'empty_labeled'
                                else:
                                    status = 'empty_labeled'
                        except Exception:
                            status = 'unlabeled'
                    
                    # 检查数据库中是否已存在该数据集中的该图片记录
                    cursor.execute("SELECT id FROM annotations WHERE dataset_id = ? AND image_path = ?", (dataset_id, image_path))
                    existing = cursor.fetchone()
                    
                    current_time = self._current_time()
                    
                    if existing:
                        # 更新现有记录
                        cursor.execute('''
                            UPDATE annotations SET
                                label_path = ?, image_md5 = ?, width = ?, height = ?, 
                                status = ?, updated_at = ?
                            WHERE id = ?
                        ''', (label_path, md5_hash, width, height, status, current_time, existing['id']))
                        
                        stats['updated'] += 1
                    else:
                        # 插入新记录（直接包含dataset_id）
                        cursor.execute('''
                            INSERT INTO annotations (
                                dataset_id, image_path, label_path, image_md5, width, height, 
                                status, created_at, updated_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (dataset_id, image_path, label_path, md5_hash, width, height, 
                              status, current_time, current_time))
                        
                        stats['added'] += 1
                        
                except Exception as e:
                    print(f"处理文件 {image_path} 时出错: {e}")
                    stats['errors'] += 1
                    continue
            
            conn.commit()
        
        return stats

    # ==================== 任务管理 (Tasks - 基本无变化) ====================
    
    def create_task(self, task_data: Dict[str, Any]) -> int:
        current_time = self._current_time()
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO tasks (
                    name, task_type, python_env_path, train_script_path,
                    test_script_path, output_dir, description,
                    train_params_definition, test_params_definition,
                    created_time, updated_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                task_data['name'], task_data['task_type'], task_data['python_env_path'],
                task_data['train_script_path'], task_data['test_script_path'],
                task_data['output_dir'], task_data.get('description', ''),
                json.dumps(task_data.get('train_params_definition', {})),
                json.dumps(task_data.get('test_params_definition', {})),
                current_time, current_time
            ))
            conn.commit()
            return cursor.lastrowid

    def get_all_tasks(self) -> List[Dict[str, Any]]:
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM tasks ORDER BY created_time DESC')
            tasks = []
            for row in cursor.fetchall():
                task_dict = self._dict_from_row(row)
                # 解析JSON字段
                if task_dict.get('train_params_definition'):
                    try:
                        if isinstance(task_dict['train_params_definition'], str):
                            task_dict['train_params_definition'] = json.loads(task_dict['train_params_definition'])
                    except (json.JSONDecodeError, TypeError):
                        task_dict['train_params_definition'] = {}
                
                if task_dict.get('test_params_definition'):
                    try:
                        if isinstance(task_dict['test_params_definition'], str):
                            task_dict['test_params_definition'] = json.loads(task_dict['test_params_definition'])
                    except (json.JSONDecodeError, TypeError):
                        task_dict['test_params_definition'] = {}
                
                tasks.append(task_dict)
            return tasks

    def delete_task(self, task_id: int) -> bool:
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM tasks WHERE id = ?', (task_id,))
            conn.commit()
            return cursor.rowcount > 0
            
    def get_task_by_id(self, task_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取任务"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM tasks WHERE id = ?', (task_id,))
            row = cursor.fetchone()
            if row:
                task_dict = self._dict_from_row(row)
                # 解析JSON字段
                if task_dict.get('train_params_definition'):
                    try:
                        task_dict['train_params_definition'] = json.loads(task_dict['train_params_definition'])
                    except (json.JSONDecodeError, TypeError):
                        task_dict['train_params_definition'] = {}
                
                if task_dict.get('test_params_definition'):
                    try:
                        task_dict['test_params_definition'] = json.loads(task_dict['test_params_definition'])
                    except (json.JSONDecodeError, TypeError):
                        task_dict['test_params_definition'] = {}
                
                return task_dict
            return None
            
    def update_task(self, task_id: int, task_data: Dict[str, Any]) -> bool:
        """更新任务"""
        current_time = self._current_time()
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE tasks SET
                    name = ?, task_type = ?, python_env_path = ?,
                    train_script_path = ?, test_script_path = ?, output_dir = ?,
                    description = ?, train_params_definition = ?,
                    test_params_definition = ?, updated_time = ?
                WHERE id = ?
            ''', (
                task_data['name'],
                task_data['task_type'],
                task_data['python_env_path'],
                task_data['train_script_path'],
                task_data['test_script_path'],
                task_data['output_dir'],
                task_data.get('description', ''),
                task_data.get('train_params_definition') if isinstance(task_data.get('train_params_definition'), str) else json.dumps(task_data.get('train_params_definition', {})),
                task_data.get('test_params_definition') if isinstance(task_data.get('test_params_definition'), str) else json.dumps(task_data.get('test_params_definition', {})),
                current_time,
                task_id
            ))
            
            conn.commit()
            return cursor.rowcount > 0

    # ==================== 模型管理 (Models - 基本无变化) ====================

    def create_model(self, model_data: Dict[str, Any]) -> int:
        current_time = self._current_time()
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # 适应更新后的数据库表结构
            model_path = model_data.get('model_path', model_data.get('onnx_path', ''))
            if not model_path:
                # 如果没有提供模型路径，使用默认路径
                model_path = f"models/{model_data['name']}.pt"
            
            # 将训练参数和指标转换为JSON字符串
            metrics_json = json.dumps(model_data.get('metrics', {}))
            train_params_json = json.dumps(model_data.get('train_params', {}))
            
            cursor.execute('''
                INSERT INTO models (
                    name, task_id, dataset_id, model_path, config_path, 
                    metrics_json, status, train_params, onnx_path, 
                    training_start_time, training_end_time, created_time, updated_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                model_data['name'], 
                model_data['task_id'], 
                model_data.get('dataset_id'),
                model_path,
                model_data.get('config_path', ''),
                metrics_json,
                model_data.get('status', 'completed'),
                train_params_json,
                model_data.get('onnx_path', ''),
                model_data.get('training_start_time', ''),
                model_data.get('training_end_time', ''),
                current_time, 
                current_time
            ))
            conn.commit()
            return cursor.lastrowid

    def get_all_models(self) -> List[Dict[str, Any]]:
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT m.*, t.task_type, t.name as task_name, d.name as dataset_name
                FROM models m
                LEFT JOIN tasks t ON m.task_id = t.id
                LEFT JOIN datasets d ON m.dataset_id = d.id
                ORDER BY m.created_time DESC
            ''')
            return [self._dict_from_row(row) for row in cursor.fetchall()]

    def delete_annotation_by_path(self, image_path: str) -> bool:
        """根据图片路径删除标注记录"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM annotations WHERE image_path = ?', (image_path,))
            conn.commit()
            return cursor.rowcount > 0



    def delete_model(self, model_id: int) -> bool:
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM models WHERE id = ?', (model_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    def update_model_testing_info(self, model_id: int, test_params: Dict[str, Any] = None, 
                                 test_metrics: Dict[str, Any] = None, test_time: str = None,
                                 tensorrt_path: str = None) -> bool:
        """更新模型的测试信息"""
        current_time = self._current_time()
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # 构建更新字段
            update_fields = []
            update_values = []
            
            if test_params is not None:
                update_fields.append("test_params = ?")
                update_values.append(json.dumps(test_params))
            
            if test_metrics is not None:
                update_fields.append("metrics_json = ?")
                update_values.append(json.dumps(test_metrics))
            
            if test_time is not None:
                update_fields.append("test_time = ?")
                update_values.append(test_time)
            
            if tensorrt_path is not None:
                update_fields.append("tensorrt_path = ?")
                update_values.append(tensorrt_path)
            
            # 总是更新修改时间
            update_fields.append("updated_time = ?")
            update_values.append(current_time)
            
            # 添加WHERE条件的参数
            update_values.append(model_id)
            
            if update_fields:
                sql = f"UPDATE models SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(sql, update_values)
                conn.commit()
                return cursor.rowcount > 0
            
            return False