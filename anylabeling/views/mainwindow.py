"""This module defines the main application window"""

from PyQt5.QtWidgets import QMainWindow, QStatusBar, QVBoxLayout, QWidget, QMenuBar, QMenu, QAction
from PyQt5.QtCore import Qt

from ..app_info import __appdescription__, __appname__
from .labeling.label_wrapper import LabelingWrapper
from .training.task_config_dialog import TaskConfigDialog
from .training.model_training_dialog import ModelTrainingDialog
from .training.model_testing_dialog import ModelTestingDialog
from .training.model_management_dialog import ModelManagementDialog
from .training.dataset_management_dialog import DatasetManagementDialog


class MainWindow(QMainWindow):
    """Main application window"""

    def __init__(
        self,
        app,
        config=None,
        filename=None,
        output=None,
        output_file=None,
        output_dir=None,
    ):
        super().__init__()
        self.app = app
        self.config = config

        # 保持对话框引用，防止被垃圾回收
        self.dialogs = {}

        self.setContentsMargins(0, 0, 0, 0)
        self.setWindowTitle(__appname__)

        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        self.labeling_widget = LabelingWrapper(
            self,
            config=config,
            filename=filename,
            output=output,
            output_file=output_file,
            output_dir=output_dir,
        )
        main_layout.addWidget(self.labeling_widget)
        widget = QWidget()
        widget.setLayout(main_layout)
        self.setCentralWidget(widget)

        status_bar = QStatusBar()
        status_bar.showMessage(f"{__appname__} - {__appdescription__}")
        self.setStatusBar(status_bar)

        # 设置菜单栏
        self.setup_menu_bar()
        
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # AI训练菜单
        ai_menu = menubar.addMenu("AI训练(&A)")
        
        # 任务配置
        task_config_action = QAction("任务配置管理(&C)", self)
        task_config_action.setStatusTip("配置和管理AI训练任务模板")
        task_config_action.triggered.connect(self.open_task_config)
        ai_menu.addAction(task_config_action)
        
        # 数据集管理
        dataset_management_action = QAction("数据集管理(&D)", self)
        dataset_management_action.setStatusTip("管理训练数据集")
        dataset_management_action.triggered.connect(self.open_dataset_management)
        ai_menu.addAction(dataset_management_action)
        
        ai_menu.addSeparator()
        
        # 模型训练
        model_training_action = QAction("模型训练(&T)", self)
        model_training_action.setStatusTip("启动AI模型训练")
        model_training_action.triggered.connect(self.open_model_training)
        ai_menu.addAction(model_training_action)
        
        # 模型测试
        model_testing_action = QAction("模型测试(&E)", self)
        model_testing_action.setStatusTip("测试已训练的AI模型")
        model_testing_action.triggered.connect(self.open_model_testing)
        ai_menu.addAction(model_testing_action)
        
        # 模型管理
        model_management_action = QAction("模型管理(&M)", self)
        model_management_action.setStatusTip("查看和管理已训练的AI模型")
        model_management_action.triggered.connect(self.open_model_management)
        ai_menu.addAction(model_management_action)
        
    def open_task_config(self):
        """打开任务配置对话框"""
        if 'task_config' not in self.dialogs or not self.dialogs['task_config'].isVisible():
            self.dialogs['task_config'] = TaskConfigDialog(None)  # 不传入父窗口，创建独立窗口
        self.dialogs['task_config'].show()  # 使用show()而不是exec_()
        self.dialogs['task_config'].raise_()  # 将窗口提到前台
        self.dialogs['task_config'].activateWindow()  # 激活窗口

    def open_model_training(self):
        """打开模型训练对话框"""
        if 'model_training' not in self.dialogs or not self.dialogs['model_training'].isVisible():
            self.dialogs['model_training'] = ModelTrainingDialog(None)  # 不传入父窗口，创建独立窗口
        self.dialogs['model_training'].show()  # 使用show()而不是exec_()
        self.dialogs['model_training'].raise_()  # 将窗口提到前台
        self.dialogs['model_training'].activateWindow()  # 激活窗口

    def open_model_testing(self):
        """打开模型测试对话框"""
        if 'model_testing' not in self.dialogs or not self.dialogs['model_testing'].isVisible():
            self.dialogs['model_testing'] = ModelTestingDialog(None)  # 不传入父窗口，创建独立窗口
        self.dialogs['model_testing'].show()  # 使用show()而不是exec_()
        self.dialogs['model_testing'].raise_()  # 将窗口提到前台
        self.dialogs['model_testing'].activateWindow()  # 激活窗口

    def open_dataset_management(self):
        """打开数据集管理对话框"""
        if 'dataset_management' not in self.dialogs or not self.dialogs['dataset_management'].isVisible():
            self.dialogs['dataset_management'] = DatasetManagementDialog(None, main_window=self)  # 不传入父窗口，但保留main_window参数
        self.dialogs['dataset_management'].show()  # 使用show()而不是exec_()
        self.dialogs['dataset_management'].raise_()  # 将窗口提到前台
        self.dialogs['dataset_management'].activateWindow()  # 激活窗口

    def open_model_management(self):
        """打开模型管理对话框"""
        if 'model_management' not in self.dialogs or not self.dialogs['model_management'].isVisible():
            self.dialogs['model_management'] = ModelManagementDialog(None)  # 不传入父窗口，创建独立窗口
        self.dialogs['model_management'].show()  # 使用show()而不是exec_()
        self.dialogs['model_management'].raise_()  # 将窗口提到前台
        self.dialogs['model_management'].activateWindow()  # 激活窗口
