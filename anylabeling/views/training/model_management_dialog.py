"""
模型管理对话框
提供模型查看、管理和删除功能
"""
import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QComboBox, QLineEdit, QMessageBox,
    QHeaderView, QAbstractItemView, QMenu, QTextEdit,
    QGroupBox, QFormLayout, QProgressBar
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QBrush

from anylabeling.utils.db_manager import DatabaseManager


# ModelDetailWidget 类已移除，使用独立的对话框替代


class ModelManagementDialog(QDialog):
    """模型管理对话框"""
    
    model_selected = pyqtSignal(dict)  # 模型选择信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.models_data = []
        self.filtered_models = []
        
        self.setWindowTitle("模型管理")
        self.setModal(False)  # 改为非模态窗口
        self.resize(1200, 800)
        
        # 设置窗口标志，创建独立的顶级窗口，添加最小化和最大化按钮
        from PyQt5.QtCore import Qt
        self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        
        self.setup_ui()
        self.load_models()
        
        # 设置定时器自动刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_models)
        self.refresh_timer.start(10000)  # 每10秒刷新一次
    
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        
        # 顶部工具栏
        self._create_toolbar(layout)
        
        # 模型列表（移除右侧详情面板）
        list_widget = self._create_model_list()
        layout.addWidget(list_widget)
        
        # 底部按钮
        self._create_buttons(layout)
    
    def _create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_layout = QHBoxLayout()
        
        # 搜索框
        toolbar_layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入模型名称或任务类型...")
        self.search_edit.textChanged.connect(self.filter_models)
        toolbar_layout.addWidget(self.search_edit)
        
        # 任务类型过滤
        toolbar_layout.addWidget(QLabel("任务类型:"))
        self.task_type_combo = QComboBox()
        self.task_type_combo.addItem("全部", "")
        self.task_type_combo.addItem("目标检测", "object_detection")
        self.task_type_combo.addItem("实例分割", "instance_segmentation")
        self.task_type_combo.addItem("语义分割", "semantic_segmentation")
        self.task_type_combo.addItem("细胞分割", "cell_segmentation")
        self.task_type_combo.currentTextChanged.connect(self.filter_models)
        toolbar_layout.addWidget(self.task_type_combo)
        
        # 状态过滤
        toolbar_layout.addWidget(QLabel("状态:"))
        self.status_combo = QComboBox()
        self.status_combo.addItem("全部", "")
        self.status_combo.addItem("训练中", "training")
        self.status_combo.addItem("已完成", "completed")
        self.status_combo.addItem("训练失败", "failed")
        self.status_combo.addItem("已停止", "stopped")
        self.status_combo.currentTextChanged.connect(self.filter_models)
        toolbar_layout.addWidget(self.status_combo)
        
        toolbar_layout.addStretch()
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.refresh_models)
        toolbar_layout.addWidget(self.refresh_button)
        
        parent_layout.addLayout(toolbar_layout)
    
    def _create_model_list(self) -> QGroupBox:
        """创建模型列表"""
        group_box = QGroupBox("模型列表")
        layout = QVBoxLayout(group_box)
        
        # 统计信息
        self.stats_label = QLabel()
        layout.addWidget(self.stats_label)
        
        # 模型表格
        self.model_table = QTableWidget()
        self.model_table.setColumnCount(8)
        self.model_table.setHorizontalHeaderLabels([
            "模型名称", "任务类型", "状态", "训练开始时间", "训练结束时间", "训练耗时", "数据集名称", "操作"
        ])
        
        # 设置表格属性
        self.model_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.model_table.setAlternatingRowColors(True)
        self.model_table.setSortingEnabled(True)
        
        # 设置列宽
        header = self.model_table.horizontalHeader()
        header.resizeSection(0, 180)  # 模型名称
        header.resizeSection(1, 100)  # 任务类型
        header.resizeSection(2, 80)   # 状态
        header.resizeSection(3, 140)  # 训练开始时间
        header.resizeSection(4, 140)  # 训练结束时间
        header.resizeSection(5, 100)  # 训练耗时
        header.resizeSection(6, 120)  # 数据集名称
        header.setStretchLastSection(True)  # 操作列自适应
        
        # 连接选择信号
        self.model_table.itemSelectionChanged.connect(self.on_model_selected)
        
        # 移除右键菜单功能
        
        layout.addWidget(self.model_table)
        
        return group_box
    
    def _create_buttons(self, parent_layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()
        
        # 统计信息
        self.total_label = QLabel()
        button_layout.addWidget(self.total_label)
        
        button_layout.addStretch()
        
        # 移除导出和最小化按钮，因为导出功能未完成，最小化按钮在标题栏已有
        # self.export_button = QPushButton("导出模型")
        # self.export_button.clicked.connect(self.export_selected_model)
        # self.export_button.setEnabled(False)
        # button_layout.addWidget(self.export_button)
        
        parent_layout.addLayout(button_layout)
    
    def load_models(self):
        """加载模型数据"""
        try:
            self.models_data = self.db_manager.get_all_models()
            self.filter_models()
            self.update_statistics()
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载模型数据失败: {str(e)}")
    
    def filter_models(self):
        """过滤模型数据"""
        search_text = self.search_edit.text().lower()
        task_type = self.task_type_combo.currentData()
        status = self.status_combo.currentData()
        
        self.filtered_models = []
        
        for model in self.models_data:
            # 搜索过滤
            if search_text:
                if (search_text not in model.get('name', '').lower() and
                    search_text not in model.get('task_type', '').lower()):
                    continue

            # 任务类型过滤
            if task_type and model.get('task_type') != task_type:
                continue
            
            # 状态过滤
            if status and model.get('status') != status:
                continue
            
            self.filtered_models.append(model)
        
        self.update_table()
        self.update_statistics()
    
    def update_table(self):
        """更新表格显示"""
        self.model_table.setRowCount(len(self.filtered_models))
        
        for row, model in enumerate(self.filtered_models):
            # 模型名称
            name_item = QTableWidgetItem(model.get('name', ''))
            self.model_table.setItem(row, 0, name_item)
            
            # 任务类型
            task_type_text = self._get_task_type_text(model.get('task_type', ''))
            task_item = QTableWidgetItem(task_type_text)
            self.model_table.setItem(row, 1, task_item)
            
            # 状态
            status = model.get('status', '')
            status_item = QTableWidgetItem(self._get_status_text(status))
            
            # 根据状态设置颜色
            if status == 'completed':
                status_item.setBackground(QBrush(QColor(200, 255, 200)))
            elif status == 'failed':
                status_item.setBackground(QBrush(QColor(255, 200, 200)))
            elif status == 'training':
                status_item.setBackground(QBrush(QColor(200, 200, 255)))
            
            self.model_table.setItem(row, 2, status_item)
            
            # 训练开始时间
            train_start = model.get('training_start_time', '')
            train_start_text = self._format_datetime(train_start) if train_start else 'N/A'
            train_start_item = QTableWidgetItem(train_start_text)
            self.model_table.setItem(row, 3, train_start_item)

            # 训练结束时间
            train_end = model.get('training_end_time', '')
            train_end_text = self._format_datetime(train_end) if train_end else 'N/A'
            train_end_item = QTableWidgetItem(train_end_text)
            self.model_table.setItem(row, 4, train_end_item)

            # 训练耗时
            duration_text = self._calculate_training_duration(train_start, train_end)
            duration_item = QTableWidgetItem(duration_text)
            self.model_table.setItem(row, 5, duration_item)

            # 数据集名称
            dataset_name = self._get_dataset_name(model.get('dataset_id', ''))
            dataset_item = QTableWidgetItem(dataset_name)
            self.model_table.setItem(row, 6, dataset_item)
            
            # 操作按钮
            self._create_action_buttons(row, model)
    
    def _create_action_buttons(self, row: int, model: Dict):
        """创建操作按钮"""
        from PyQt5.QtWidgets import QWidget

        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)

        # 查看按钮 - 弹出详情对话框
        view_btn = QPushButton("查看")
        # 使用默认参数避免闭包问题
        view_btn.clicked.connect(lambda checked, m=model: self.show_model_detail_dialog(m))
        layout.addWidget(view_btn)

        # 删除按钮
        delete_btn = QPushButton("删除")
        # 使用默认参数避免闭包问题
        delete_btn.clicked.connect(lambda checked, m=model: self.delete_model(m))
        layout.addWidget(delete_btn)

        self.model_table.setCellWidget(row, 7, widget)
    
    def update_statistics(self):
        """更新统计信息"""
        total_models = len(self.models_data)
        filtered_count = len(self.filtered_models)
        
        # 状态统计
        status_counts = {}
        for model in self.models_data:
            status = model.get('status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # 更新显示
        stats_text = f"总计: {total_models} 个模型"
        if filtered_count != total_models:
            stats_text += f" (显示: {filtered_count})"
        
        self.stats_label.setText(stats_text)
        
        # 底部统计
        total_text = f"完成: {status_counts.get('completed', 0)} | "
        total_text += f"训练中: {status_counts.get('training', 0)} | "
        total_text += f"失败: {status_counts.get('failed', 0)}"
        
        self.total_label.setText(total_text)
    
    def on_model_selected(self):
        """模型选择变化"""
        selected_rows = set()
        for item in self.model_table.selectedItems():
            selected_rows.add(item.row())

        has_selection = len(selected_rows) > 0

        # 注意：这里不需要更新按钮状态，因为删除按钮在每行的操作列中
        # 如果将来需要底部的删除按钮，可以在这里添加逻辑

    def show_model_detail_dialog(self, model: Dict[str, Any]):
        """显示模型详情对话框"""
        try:
            from .model_detail_dialog import ModelDetailDialog
            dialog = ModelDetailDialog(model, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.warning(self, "错误", f"打开模型详情失败: {str(e)}")
    

    
    def delete_model(self, model_data):
        """删除单个模型"""
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除模型 '{model_data['name']}' 吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.delete_model(model_data['id'])
                self.refresh_models()
                # 删除成功后不弹窗，只在状态栏或日志中显示即可
                # 用户已经看到模型从列表中消失，这就是最好的反馈
            except Exception as e:
                QMessageBox.warning(self, "删除失败", f"删除模型失败: {str(e)}")
    

    
    # 移除导出功能，因为未完成
    # def export_selected_model(self):
    #     """导出选中的模型"""
    #     selected_rows = set(item.row() for item in self.model_table.selectedItems())
    #     if len(selected_rows) != 1:
    #         QMessageBox.information(self, "提示", "请选择一个模型进行导出")
    #         return
    #
    #     row = list(selected_rows)[0]
    #     model_data = self.filtered_models[row]
    #
    #     # 这里实现模型导出功能
    #     # 暂时显示提示信息
    #     QMessageBox.information(
    #         self, "导出模型",
    #         f"模型导出功能开发中...\n选中模型: {model_data['name']}"
    #     )
    
    def refresh_models(self):
        """刷新模型列表"""
        self.load_models()
    
    def _get_task_type_text(self, task_type: str) -> str:
        """获取任务类型显示文本"""
        type_map = {
            'object_detection': '目标检测',
            'instance_segmentation': '实例分割',
            'semantic_segmentation': '语义分割',
            'cell_segmentation': '细胞分割'
        }
        return type_map.get(task_type, task_type)
    
    def _get_status_text(self, status: str) -> str:
        """获取状态显示文本"""
        status_map = {
            'training': '训练中',
            'completed': '已完成',
            'failed': '训练失败',
            'stopped': '已停止',
            'testing': '测试中'
        }
        return status_map.get(status, status)
    
    def _get_metrics_summary(self, metrics: dict) -> str:
        """获取指标摘要"""
        try:
            if not metrics or not isinstance(metrics, dict):
                return "无"

            # 提取关键指标
            summary_parts = []

            # 目标检测指标
            if 'mAP' in metrics:
                summary_parts.append(f"mAP: {metrics['mAP']:.3f}")
            elif 'mAP50' in metrics:
                summary_parts.append(f"mAP50: {metrics['mAP50']:.3f}")

            # 分割指标
            if 'IoU' in metrics:
                summary_parts.append(f"IoU: {metrics['IoU']:.3f}")
            elif 'mIoU' in metrics:
                summary_parts.append(f"mIoU: {metrics['mIoU']:.3f}")

            # 通用指标
            if 'accuracy' in metrics:
                summary_parts.append(f"Acc: {metrics['accuracy']:.3f}")

            # 如果有其他指标，显示前几个
            if not summary_parts and metrics:
                for key, value in list(metrics.items())[:2]:
                    if isinstance(value, (int, float)):
                        summary_parts.append(f"{key}: {value:.3f}")

            return " | ".join(summary_parts) if summary_parts else "有指标"

        except Exception as e:
            return f"解析错误: {str(e)}"

    def _format_datetime(self, datetime_str: str) -> str:
        """格式化日期时间显示"""
        if not datetime_str:
            return 'N/A'

        try:
            from datetime import datetime
            # 解析ISO格式的时间字符串
            dt = datetime.fromisoformat(datetime_str.replace('T', ' ').replace('Z', ''))
            return dt.strftime('%Y-%m-%d %H:%M')
        except:
            # 如果解析失败，返回原始字符串的简化版本
            return datetime_str.split('T')[0] if 'T' in datetime_str else datetime_str

    def _calculate_training_duration(self, start_time: str, end_time: str) -> str:
        """计算训练耗时"""
        if not start_time or not end_time:
            return 'N/A'

        try:
            from datetime import datetime
            start_dt = datetime.fromisoformat(start_time.replace('T', ' ').replace('Z', ''))
            end_dt = datetime.fromisoformat(end_time.replace('T', ' ').replace('Z', ''))

            duration = end_dt - start_dt

            # 格式化显示
            total_seconds = int(duration.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            seconds = total_seconds % 60

            if hours > 0:
                return f"{hours}h {minutes}m {seconds}s"
            elif minutes > 0:
                return f"{minutes}m {seconds}s"
            else:
                return f"{seconds}s"
        except:
            return 'N/A'

    def _get_dataset_name(self, dataset_id: str) -> str:
        """获取数据集名称"""
        import logging

        # 设置日志记录
        logger = logging.getLogger(__name__)

        if not dataset_id:
            logger.debug("数据集ID为空")
            return 'N/A'

        # 处理不同类型的dataset_id
        try:
            if isinstance(dataset_id, str):
                if dataset_id.strip() == '' or dataset_id.lower() == 'none':
                    logger.debug(f"数据集ID为空字符串或None: {dataset_id}")
                    return 'N/A'
                dataset_id_int = int(dataset_id)
            elif isinstance(dataset_id, (int, float)):
                dataset_id_int = int(dataset_id)
            else:
                logger.warning(f"数据集ID类型不支持: {type(dataset_id)}, 值: {dataset_id}")
                return f'数据集{dataset_id}'
        except (ValueError, TypeError) as e:
            logger.error(f"数据集ID转换失败: {dataset_id}, 错误: {e}")
            return f'数据集{dataset_id}'

        try:
            # 尝试多种数据库查询方法
            dataset = None

            # 方法1: 尝试 get_dataset
            if hasattr(self.db_manager, 'get_dataset'):
                try:
                    dataset = self.db_manager.get_dataset(dataset_id_int)
                    logger.debug(f"使用get_dataset查询成功: {dataset}")
                except Exception as e:
                    logger.warning(f"get_dataset查询失败: {e}")

            # 方法2: 尝试 get_dataset
            if not dataset and hasattr(self.db_manager, 'get_dataset'):
                try:
                    dataset = self.db_manager.get_dataset(dataset_id_int)
                    logger.debug(f"使用get_dataset查询成功: {dataset}")
                except Exception as e:
                    logger.warning(f"get_dataset查询失败: {e}")

            # 处理查询结果
            if dataset:
                dataset_name = dataset.get('name', f'数据集{dataset_id_int}')
                logger.debug(f"成功获取数据集名称: {dataset_name}")
                return dataset_name
            else:
                logger.warning(f"未找到数据集: ID={dataset_id_int}")
                return f'数据集{dataset_id_int}'

        except Exception as e:
            logger.error(f"获取数据集名称时发生未知错误: {e}", exc_info=True)
            return f'数据集{dataset_id}'

    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止定时器
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
        
        event.accept()