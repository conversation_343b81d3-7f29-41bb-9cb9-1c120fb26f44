#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from datetime import datetime
from typing import Dict, Any, Optional

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLabel, QLineEdit, QPushButton, QComboBox,
    QProgressBar, QCheckBox, QMessageBox, QPlainTextEdit,
    QFileDialog, QSplitter
)
from PyQt5.QtCore import Qt, QProcess, pyqtSignal
from PyQt5.QtGui import QFont

from anylabeling.utils.db_manager import DatabaseManager
from anylabeling.views.training.parameter_editor_dialog import (
    ParameterEditorDialog, ParameterDisplayWidget
)


class ModelTrainingDialog(QDialog):
    """模型训练对话框"""
    
    training_finished = pyqtSignal(dict)  # 训练完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.training_process = None
        self.current_config = None
        
        self.setWindowTitle("模型训练")
        self.setModal(False)  # 改为非模态窗口
        self.resize(900, 700)
        
        # 设置窗口标志，创建独立的顶级窗口
        from PyQt5.QtCore import Qt
        self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        
        self.setup_ui()
        self.load_tasks()
        self.load_datasets()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # 上半部分：配置区域
        config_widget = self.create_config_widget()
        splitter.addWidget(config_widget)
        
        # 下半部分：训练区域
        training_widget = self.create_training_widget()
        splitter.addWidget(training_widget)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始训练")
        self.start_button.clicked.connect(self.start_training)
        # 设置开始按钮的样式
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止训练")
        self.stop_button.clicked.connect(self.stop_training)
        self.stop_button.setEnabled(False)
        # 设置停止按钮的样式
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170c;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.stop_button)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
    def create_config_widget(self):
        """创建配置区域"""
        widget = QGroupBox("训练配置")
        layout = QVBoxLayout(widget)
        
        # 基本配置
        basic_group = QGroupBox("基本配置")
        basic_layout = QFormLayout(basic_group)
        
        # 任务选择
        self.task_combo = QComboBox()
        self.task_combo.currentTextChanged.connect(self.on_task_changed)
        basic_layout.addRow("训练任务:", self.task_combo)
        
        # 模型名称
        self.model_name_edit = QLineEdit()
        self.model_name_edit.setPlaceholderText("输入模型名称，如：yolo_detection_20240107")
        basic_layout.addRow("模型名称:", self.model_name_edit)
        
        # 数据集选择
        self.dataset_combo = QComboBox()
        self.dataset_combo.currentTextChanged.connect(self.on_dataset_changed)
        basic_layout.addRow("训练数据集:", self.dataset_combo)
        
        # 数据集信息显示
        self.dataset_info_label = QLabel("请选择数据集")
        self.dataset_info_label.setStyleSheet("color: #666; font-size: 12px;")
        basic_layout.addRow("数据集信息:", self.dataset_info_label)
        
        layout.addWidget(basic_group)
        
        # 训练参数（用结构化控件展示）
        params_group = QGroupBox("训练参数")
        params_layout = QVBoxLayout(params_group)

        self.parameter_widget = ParameterDisplayWidget()
        self.parameter_widget.edit_requested.connect(self.edit_parameters)
        params_layout.addWidget(self.parameter_widget)

        layout.addWidget(params_group)
        
        return widget
        
    def create_training_widget(self):
        """创建训练区域"""
        widget = QGroupBox("训练状态")
        layout = QVBoxLayout(widget)
        
        # 状态信息
        status_layout = QHBoxLayout()
        
        status_layout.addWidget(QLabel("状态:"))
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("font-weight: bold; color: #007acc;")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.completion_checkbox = QCheckBox("训练完成后提示")
        self.completion_checkbox.setChecked(True)
        status_layout.addWidget(self.completion_checkbox)
        
        # 清除日志按钮放在训练完成后提示的旁边
        self.clear_button = QPushButton("清除日志")
        self.clear_button.clicked.connect(self.clear_log)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px 12px;
                font-size: 12px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        status_layout.addWidget(self.clear_button)
        
        layout.addLayout(status_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        log_label = QLabel("训练日志:")
        layout.addWidget(log_label)
        
        self.log_text = QPlainTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        return widget
        
    def load_tasks(self):
        """加载任务列表"""
        try:
            # 使用数据库加载任务
            tasks = self.db_manager.get_all_tasks()
            self.task_combo.clear()
            
            for task in tasks:
                self.task_combo.addItem(task['name'], task)
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载任务列表失败: {str(e)}")
    
    def load_datasets(self):
        """加载数据集列表"""
        try:
            datasets = self.db_manager.get_all_datasets()
            self.dataset_combo.clear()
            
            # 显示所有数据集（移除status检查，因为数据库中可能没有这个字段）
            for dataset in datasets:
                display_name = dataset['name']  # 只显示数据集名称
                self.dataset_combo.addItem(display_name, dataset)
                    
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载数据集列表失败: {str(e)}")
            
    def on_task_changed(self):
        """任务改变时的处理"""
        task_data = self.task_combo.currentData()
        if not task_data:
            return
            
        try:
            # 使用数据库字典的属性
            train_params_def = task_data.get('train_params_definition', {})
            
            # 取默认值
            default_params = {}
            for name, pdef in train_params_def.items():
                if isinstance(pdef, dict):
                    default_params[name] = pdef.get('default')
                else:
                    default_params[name] = pdef

            self.current_train_params_def = train_params_def
            self.parameter_widget.update_parameters(default_params)
            
            # 自动生成模型名称
            task_type = task_data.get('task_type', 'model')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            suggested_name = f"{task_type}_{timestamp}"
            self.model_name_edit.setText(suggested_name)
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载任务参数失败: {str(e)}")
    
    def edit_parameters(self):
        """编辑训练参数"""
        try:
            if not hasattr(self, 'current_train_params_def'):
                QMessageBox.information(self, "提示", "请先选择训练任务")
                return
            
            if not self.current_train_params_def:
                QMessageBox.information(self, "提示", "当前任务没有可编辑的参数")
                return
            
            print(f"参数定义类型: {type(self.current_train_params_def)}")
            print(f"参数定义内容: {self.current_train_params_def}")
            
            current_params = self.parameter_widget.get_parameters()
            print(f"当前参数: {current_params}")
            
            dialog = ParameterEditorDialog(
                self, 
                title="编辑训练参数",
                parameter_definitions=self.current_train_params_def,
                current_values=current_params
            )
            
            dialog.parameters_updated.connect(self.parameter_widget.update_parameters)
            dialog.exec_()
            
        except Exception as e:
            print(f"编辑参数时出错: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"编辑参数时出错: {str(e)}")
            
    def on_dataset_changed(self):
        """数据集改变时的处理"""
        dataset_data = self.dataset_combo.currentData()
        if not dataset_data:
            self.dataset_info_label.setText("请选择数据集")
            return
            
        try:
            # 获取数据集统计信息
            stats = self.db_manager.get_dataset_stats(dataset_data['id'])
            
            # 获取标签统计信息
            label_stats = self.db_manager.get_dataset_label_stats(dataset_data['id'])
            
            # 构建标签信息字符串
            if label_stats:
                # 按数量排序标签，只显示前3个
                sorted_labels = sorted(label_stats.items(), key=lambda x: x[1], reverse=True)
                label_info = ", ".join([f"{label}({count})" for label, count in sorted_labels[:3]])
                if len(sorted_labels) > 3:
                    label_info += "..."
            else:
                label_info = "无标签"
            
            # 显示数据集信息 - 使用正确的字段名
            info_text = (f"图片: {stats['total_images']} 张, "
                        f"已标注: {stats['labeled_images']} 个, "
                        f"标签: {label_info}, "
                        f"路径: {dataset_data.get('output_dir', '未知')}")
            
            # 如果信息太长，截断显示
            if len(info_text) > 120:
                info_text = info_text[:117] + "..."
            
            self.dataset_info_label.setText(info_text)
            
            # 如果有标签信息，设置工具提示显示完整的标签列表
            if label_stats:
                all_labels = ", ".join([f"{label}({count})" for label, count in sorted_labels])
                tooltip_text = f"标签详情:\n{all_labels}"
                self.dataset_info_label.setToolTip(tooltip_text)
            else:
                self.dataset_info_label.setToolTip("")
                
        except Exception as e:
            # 如果获取统计信息失败，显示基本信息
            try:
                info_text = f"数据集: {dataset_data.get('name', '未知')}, 路径: {dataset_data.get('output_dir', '未知')}"
                self.dataset_info_label.setText(info_text)
            except:
                self.dataset_info_label.setText("数据集信息获取失败")
            print(f"获取数据集信息失败: {e}")
        
    def start_training(self):
        """开始训练"""
        if not self.validate_inputs():
            return
            
        try:
            # 获取训练配置
            config = self.get_training_config()
            if not config:
                return
                
            self.current_config = config
            
            # 更新UI状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("训练中...")
            self.status_label.setStyleSheet("font-weight: bold; color: #ff6600;")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 清除之前的日志
            self.log_text.clear()
            self.log_text.appendPlainText(f"开始训练模型: {config['model_name']}")
            self.log_text.appendPlainText(f"任务类型: {config['task_type']}")
            self.log_text.appendPlainText(f"数据集: {config['dataset_path']}")
            self.log_text.appendPlainText("-" * 50)
            
            # 启动训练进程
            self.start_training_process(config)
            
        except Exception as e:
            self.training_failed(f"启动训练失败: {str(e)}")
            
    def start_training_process(self, config: Dict[str, Any]):
        """启动训练进程"""
        try:
            # 创建进程
            self.training_process = QProcess(self)
            self.training_process.readyReadStandardOutput.connect(self.read_stdout)
            self.training_process.readyReadStandardError.connect(self.read_stderr)
            self.training_process.finished.connect(self.training_process_finished)
            
            # 准备命令和参数
            python_env = config.get('python_env', 'python')
            train_script = config.get('train_script', '')
            
            if not train_script or not os.path.exists(train_script):
                raise Exception(f"训练脚本不存在: {train_script}")
            
            # 创建配置文件
            config_file = self.create_training_config_file(config)
            
            # 启动进程
            args = [train_script, '--config', config_file]
            self.training_process.start(python_env, args)
            
            if not self.training_process.waitForStarted(3000):
                raise Exception("无法启动训练进程")
            
            # 记录训练开始时间
            self.training_start_time = datetime.now().isoformat()
                
            self.log_text.appendPlainText(f"训练进程已启动: {python_env} {' '.join(args)}")
            self.log_text.appendPlainText(f"训练开始时间: {self.training_start_time}")
            
        except Exception as e:
            self.training_failed(f"启动训练进程失败: {str(e)}")
            
    def _safe_filename(self, name: str) -> str:
        """生成安全的文件名，处理中文和特殊字符"""
        import re
        import unicodedata
        
        # 移除或替换不安全的字符
        # 保留中文字符、英文字母、数字、下划线、连字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', name)  # 替换Windows不允许的字符
        safe_name = re.sub(r'\s+', '_', safe_name)  # 替换空格为下划线
        safe_name = re.sub(r'_{2,}', '_', safe_name)  # 多个下划线合并为一个
        safe_name = safe_name.strip('_')  # 移除首尾下划线
        
        # 如果处理后为空或过长，使用时间戳
        if not safe_name or len(safe_name) > 100:
            safe_name = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        return safe_name
    
    def create_training_config_file(self, config: Dict[str, Any]) -> str:
        """创建训练配置文件"""
        config_dir = "AI_ENV/temp"
        os.makedirs(config_dir, exist_ok=True)

        # 使用模型名称作为配置文件名
        model_name = config.get('model_name', 'unknown_model')
        safe_model_name = self._safe_filename(model_name)
        config_file = os.path.join(config_dir, f"train_config_{safe_model_name}.json")
        
        # 如果文件已存在，添加时间戳避免冲突
        if os.path.exists(config_file):
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            config_file = os.path.join(config_dir, f"train_config_{safe_model_name}_{timestamp}.json")

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        return config_file
        
    def read_stdout(self):
        """读取标准输出"""
        if not self.training_process:
            return
            
        data = self.training_process.readAllStandardOutput()
        text = data.data().decode('utf-8', errors='ignore')
        
        for line in text.strip().split('\n'):
            if line.strip():
                self.process_output_line(line.strip())
                
    def read_stderr(self):
        """读取标准错误"""
        if not self.training_process:
            return
            
        data = self.training_process.readAllStandardError()
        text = data.data().decode('utf-8', errors='ignore')
        
        for line in text.strip().split('\n'):
            if line.strip():
                self.log_text.appendPlainText(f"[ERROR] {line.strip()}")
                
    def process_output_line(self, line: str):
        """处理输出行"""
        try:
            # 尝试解析JSON格式的训练结果
            if line.startswith('{') and line.endswith('}'):
                data = json.loads(line)
                
                # 检查是否是训练结果
                if 'status' in data:
                    status = data.get('status', '')
                    if status == 'success':
                        self.training_completed(data)
                        return
                    elif status == 'error':
                        self.training_failed(data.get('error_message', '训练失败'))
                        return
                
                # 检查是否是进度信息
                msg_type = data.get('type', '')
                if msg_type == 'progress':
                    progress = data.get('value', 0)
                    message = data.get('message', '')
                    self.progress_bar.setValue(int(progress))
                    if message:
                        self.log_text.appendPlainText(f"[进度] {message}")
                        
                elif msg_type == 'log':
                    message = data.get('message', '')
                    self.log_text.appendPlainText(f"[日志] {message}")
                    
                elif msg_type == 'result':
                    status = data.get('status', '')
                    if status == 'completed':
                        self.training_completed(data)
                    elif status == 'failed':
                        self.training_failed(data.get('message', '训练失败'))
                        
                elif msg_type == 'error':
                    error_msg = data.get('message', '未知错误')
                    self.training_failed(error_msg)
                    
            else:
                # 普通文本输出
                self.log_text.appendPlainText(line)
                
        except json.JSONDecodeError:
            # 不是JSON格式，直接显示
            self.log_text.appendPlainText(line)
            

        
    def training_completed(self, result_data: Dict[str, Any]):
        """训练完成（通过JSON消息触发）"""
        # 只更新UI状态和记录日志，不重复保存模型和显示弹窗
        # 实际的完成处理由 training_process_finished 方法统一处理
        self.log_text.appendPlainText("[JSON] 收到训练完成消息")
        if result_data.get('metrics'):
            self.log_text.appendPlainText(f"[JSON] 训练指标: {result_data['metrics']}")
        
        # 更新进度条
        self.progress_bar.setValue(100)
            
    def training_failed(self, error_message: str):
        """训练失败"""
        self.status_label.setText("训练失败")
        self.status_label.setStyleSheet("font-weight: bold; color: #cc0000;")
        self.log_text.appendPlainText(f"[错误] {error_message}")
        
        # 重置UI状态
        self.reset_ui_state()
        
        # 标记已经处理过错误，避免重复显示对话框
        self._error_handled = True
        
        # 显示错误提示
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.critical(self, "训练失败", error_message)
        
    def training_process_finished(self, exit_code, exit_status):
        """训练进程结束处理"""
        try:
            if exit_code == 0:
                self.log_text.appendPlainText("训练进程正常结束")
                self.status_label.setText("训练完成")
                self.status_label.setStyleSheet("font-weight: bold; color: #00aa00;")
                
                # 尝试从输出目录获取训练结果并保存到数据库
                self.save_training_result_to_database()
                
                # 不显示退出码的弹窗，只在勾选了"训练完成后提示"时显示简洁的完成消息
                if self.completion_checkbox.isChecked():
                    QMessageBox.information(self, "训练完成", 
                                          f"模型 '{self.current_config.get('model_name', '未知模型')}' 训练完成！")
            else:
                self.log_text.appendPlainText(f"训练进程异常结束，退出码: {exit_code}")
                self.status_label.setText("训练失败")
                self.status_label.setStyleSheet("font-weight: bold; color: #cc0000;")
                
                # 只有在没有处理过错误时才显示错误对话框
                if not hasattr(self, '_error_handled') or not self._error_handled:
                    QMessageBox.warning(self, "训练异常", 
                                       f"训练进程异常结束！\n退出码: {exit_code}")
                
        except Exception as e:
            self.log_text.appendPlainText(f"处理训练结束事件失败: {str(e)}")
        finally:
            # 重置UI状态和错误处理标记
            self.reset_ui_state()
            if hasattr(self, '_error_handled'):
                delattr(self, '_error_handled')
    
    def save_training_result_to_database(self):
        """将训练结果保存到数据库"""
        try:
            if not self.current_config:
                return
                
            model_name = self.current_config['model_name']
            
            # 构建输出目录路径
            output_base_dir = "AI_ENV/output/object_detection"
            model_output_dir = os.path.join(output_base_dir, model_name)
            
            # 查找模型文件
            model_path = ""
            onnx_path = ""
            
            # 查找.pt模型文件
            weights_dir = os.path.join(model_output_dir, "train", "weights")
            if os.path.exists(weights_dir):
                best_pt = os.path.join(weights_dir, "best.pt")
                if os.path.exists(best_pt):
                    model_path = best_pt
            
            # 查找ONNX文件
            onnx_file = os.path.join(model_output_dir, f"{model_name}.onnx")
            if os.path.exists(onnx_file):
                onnx_path = onnx_file
            
            # 读取训练指标（如果存在）
            metrics = {}
            metrics_file = os.path.join(model_output_dir, "training_metrics.json")
            if os.path.exists(metrics_file):
                try:
                    with open(metrics_file, 'r', encoding='utf-8') as f:
                        metrics = json.load(f)
                except:
                    pass
            
            # 保存模型信息到数据库
            model_data = {
                'name': model_name,
                'task_id': self.current_config.get('task_id', 1),  # 默认任务ID
                'dataset_id': self.current_config.get('dataset_id'),
                'model_path': model_path,  # 使用model_path而不是onnx_path
                'onnx_path': onnx_path,  # 保留onnx_path用于create_model方法
                'config_path': os.path.join(model_output_dir, "training_config.json"),
                'metrics': metrics,  # 直接传递字典，不需要JSON序列化
                'status': 'completed',
                'train_params': self.current_config.get('train_params', {}),
                'training_start_time': getattr(self, 'training_start_time', ''),
                'training_end_time': datetime.now().isoformat()
            }
            
            model_id = self.db_manager.create_model(model_data)
            if model_id:
                self.log_text.appendPlainText(f"模型已保存到数据库，ID: {model_id}")
                self.log_text.appendPlainText(f"模型文件: {model_path}")
                self.log_text.appendPlainText(f"ONNX文件: {onnx_path}")
            else:
                self.log_text.appendPlainText("保存模型到数据库失败")
                
        except Exception as e:
            self.log_text.appendPlainText(f"保存训练结果到数据库失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def reset_ui_state(self):
        """重置UI状态"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        if self.training_process:
            self.training_process.deleteLater()
            self.training_process = None
            
    def stop_training(self):
        """停止训练"""
        if not self.training_process:
            return
            
        try:
            self.log_text.appendPlainText("正在停止训练...")
            self.training_process.terminate()
            
            # 等待进程结束
            if not self.training_process.waitForFinished(5000):
                self.training_process.kill()
                self.log_text.appendPlainText("强制终止训练进程")
            else:
                self.log_text.appendPlainText("训练已停止")
                
            self.status_label.setText("已停止")
            self.status_label.setStyleSheet("font-weight: bold; color: #666;")
            
        except Exception as e:
            # 记录错误到日志，避免弹窗干扰用户
            self.log_text.appendPlainText(f"[错误] 停止训练失败: {str(e)}")
        finally:
            # 确保UI状态被重置
            self.reset_ui_state()
            
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
        
    def validate_inputs(self):
        """验证输入"""
        # 避免重复弹窗，使用单一的验证消息
        errors = []
        
        if not self.model_name_edit.text().strip():
            errors.append("请输入模型名称")
            
        # 检查是否选择了数据集
        if self.dataset_combo.currentData() is None:
            errors.append("请选择数据集")
        
        if errors:
            QMessageBox.warning(self, "输入验证", "\n".join(errors))
            return False
            
        return True
        
    def get_training_config(self):
        """获取训练配置"""
        try:
            # 获取选中的任务和数据集
            task_data = self.task_combo.currentData()
            dataset_data = self.dataset_combo.currentData()

            if not task_data or not dataset_data:
                return None

            train_params = self.parameter_widget.get_parameters() if hasattr(self, 'parameter_widget') else {}

            # 通过路径查找正确的数据集ID
            dataset_path = dataset_data.get('processed_path', dataset_data.get('path', ''))
            correct_dataset_id = self.find_dataset_id_by_path(dataset_path)

            config = {
                'model_name': self.model_name_edit.text().strip(),
                'task_id': task_data['id'],
                'task_type': task_data['task_type'],
                'dataset_id': correct_dataset_id,
                'dataset_path': dataset_path,
                'output_dir': task_data.get('output_dir', 'models'),
                'python_env': task_data.get('python_env_path', 'python'),
                'train_script': task_data.get('train_script_path', ''),
                'train_params': train_params
            }

            return config
            
        except json.JSONDecodeError as e:
            # 记录错误到日志，避免弹窗干扰训练流程
            self.log_text.appendPlainText(f"[错误] 训练参数JSON格式错误: {str(e)}")
            return None
        except Exception as e:
            # 记录错误到日志，避免弹窗干扰训练流程
            self.log_text.appendPlainText(f"[错误] 获取训练配置失败: {str(e)}")
            return None

    def find_dataset_id_by_path(self, dataset_path: str) -> int:
        """通过路径查找正确的数据集ID"""
        try:
            # 标准化路径
            normalized_path = dataset_path.replace('\\', '/')

            # 获取所有数据集
            datasets = self.db_manager.get_all_datasets()

            # 查找匹配的数据集
            for dataset in datasets:
                db_path = dataset.get('path', '').replace('\\', '/')
                if db_path == normalized_path:
                    return dataset.get('id', 1)

            # 如果没找到匹配的，返回第一个数据集的ID，或者默认为1
            if datasets:
                return datasets[0].get('id', 1)

            return 1  # 默认返回1

        except Exception as e:
            print(f"查找数据集ID时出错: {e}")
            return 1  # 出错时返回默认值