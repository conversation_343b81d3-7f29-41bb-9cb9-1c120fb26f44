"""
模型测试对话框
提供模型测试的用户界面和流程控制
"""
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QComboBox, QLineEdit, QPushButton, QProgressBar,
    QPlainTextEdit, QLabel, QGroupBox, QSplitter,
    QMessageBox, QFileDialog, QTextEdit
)
from PyQt5.QtCore import Qt, QProcess, pyqtSignal
from PyQt5.QtGui import QFont, QTextCursor

from anylabeling.utils.db_manager import DatabaseManager
from anylabeling.views.training.parameter_editor_dialog import (
    ParameterEditorDialog, ParameterDisplayWidget
)


class ModelTestingDialog(QDialog):
    """模型测试对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.testing_process = None
        self.current_model_id = None
        
        self.setWindowTitle("模型测试")
        self.setModal(False)  # 改为非模态窗口
        self.resize(900, 700)
        
        # 设置窗口标志，创建独立的顶级窗口
        self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        
        self.setup_ui()
        self.load_models()
    
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # 上半部分：配置区域
        config_widget = self._create_config_widget()
        splitter.addWidget(config_widget)
        
        # 下半部分：测试监控和结果区域
        monitor_widget = self._create_monitor_widget()
        splitter.addWidget(monitor_widget)
        
        # 设置分割器比例
        splitter.setSizes([300, 400])
        
        # 底部按钮
        self._create_buttons(layout)
    
    def _create_config_widget(self) -> QGroupBox:
        """创建配置区域"""
        group_box = QGroupBox("测试配置")
        layout = QVBoxLayout(group_box)
        
        # 模型选择
        model_form = QFormLayout()
        
        self.model_combo = QComboBox()
        self.model_combo.currentTextChanged.connect(self.on_model_changed)
        model_form.addRow("选择模型:", self.model_combo)
        
        # 模型信息显示
        self.model_info_text = QTextEdit()
        self.model_info_text.setReadOnly(True)
        self.model_info_text.setMaximumHeight(100)
        model_form.addRow("模型信息:", self.model_info_text)
        
        layout.addLayout(model_form)
        
        # 测试数据配置
        data_group = QGroupBox("测试数据配置")
        data_layout = QFormLayout(data_group)
        
        # 测试图片路径
        input_layout = QHBoxLayout()
        self.input_path_edit = QLineEdit()
        input_browse_btn = QPushButton("浏览")
        input_browse_btn.clicked.connect(self.browse_input_path)
        input_layout.addWidget(self.input_path_edit)
        input_layout.addWidget(input_browse_btn)
        data_layout.addRow("测试图片路径:", input_layout)
        
        # 结果输出路径
        output_layout = QHBoxLayout()
        self.output_path_edit = QLineEdit()
        output_browse_btn = QPushButton("浏览")
        output_browse_btn.clicked.connect(self.browse_output_path)
        output_layout.addWidget(self.output_path_edit)
        output_layout.addWidget(output_browse_btn)
        data_layout.addRow("结果输出路径:", output_layout)
        
        layout.addWidget(data_group)
        
        # 参数配置
        self.parameter_widget = ParameterDisplayWidget()
        self.parameter_widget.edit_requested.connect(self.edit_parameters)
        layout.addWidget(self.parameter_widget)
        
        return group_box
    
    def _create_monitor_widget(self) -> QGroupBox:
        """创建监控区域"""
        group_box = QGroupBox("测试状态")
        layout = QVBoxLayout(group_box)
        
        # 状态和进度
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("准备就绪")
        status_font = QFont()
        status_font.setBold(True)
        self.status_label.setFont(status_font)
        self.status_label.setStyleSheet("color: #007acc;")
        status_layout.addWidget(QLabel("状态:"))
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        layout.addLayout(status_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志显示
        log_layout = QVBoxLayout()
        
        log_header = QHBoxLayout()
        log_header.addWidget(QLabel("测试日志:"))
        log_header.addStretch()
        
        self.clear_log_btn = QPushButton("清除日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        self.clear_log_btn.setStyleSheet("""
                   QPushButton {
                       background-color: #2196F3;
                       color: white;
                       border: none;
                       padding: 6px 12px;
                       font-size: 12px;
                       border-radius: 3px;
                   }
                   QPushButton:hover {
                       background-color: #1976D2;
                   }
                   QPushButton:pressed {
                       background-color: #1565C0;
                   }
               """)
        log_header.addWidget(self.clear_log_btn)
        
        log_layout.addLayout(log_header)
        
        self.log_text = QPlainTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumBlockCount(1000)  # 限制日志行数
        log_layout.addWidget(self.log_text)
        
        layout.addLayout(log_layout)
        
        return group_box
    
    def _create_buttons(self, parent_layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()
        
        button_layout.addStretch()
        
        # 控制按钮
        self.start_button = QPushButton("开始测试")
        self.start_button.clicked.connect(self.start_testing)
        # 设置开始按钮的样式
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止测试")
        self.stop_button.clicked.connect(self.stop_testing)
        self.stop_button.setEnabled(False)
        # 设置停止按钮的样式
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170c;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.stop_button)
        
        parent_layout.addLayout(button_layout)
    
    def load_models(self):
        """加载可测试的模型列表"""
        try:
            # 只加载已完成训练的模型
            models = self.db_manager.get_all_models()
            completed_models = [m for m in models if m['status'] == 'completed']
            
            self.model_combo.clear()
            
            for model in completed_models:
                display_name = f"{model['name']} ({model['task_type']})"
                self.model_combo.addItem(display_name, model)
            
            if completed_models:
                self.on_model_changed(self.model_combo.itemText(0))
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载模型列表失败: {str(e)}")
    
    def on_model_changed(self, model_name: str):
        """模型选择变化"""
        model_data = self.model_combo.currentData()
        if not model_data:
            return
        
        self.current_model_id = model_data['id']
        
        # 获取数据集名称
        dataset_name = "未知"
        try:
            if model_data.get('dataset_id'):
                dataset = self.db_manager.get_dataset(model_data['dataset_id'])
                if dataset:
                    dataset_name = dataset.get('name', '未知')
        except Exception:
            pass
        
        # 显示模型信息
        info_text = f"任务类型: {model_data['task_type']}\n"
        info_text += f"训练数据集: {dataset_name}\n"
        info_text += f"训练开始时间: {model_data.get('training_start_time', 'N/A')}\n"
        info_text += f"训练结束时间: {model_data.get('training_end_time', 'N/A')}\n"
        info_text += f"ONNX路径: {model_data.get('onnx_path', '未生成')}"
        self.model_info_text.setPlainText(info_text)
        
        # 加载测试参数定义
        try:
            task = self.db_manager.get_task_by_id(model_data['task_id'])
            if task:
                def safe_parse(s):
                    try:
                        d = json.loads(s) if isinstance(s, str) else (s or {})
                        if isinstance(d, str):
                            d = json.loads(d)
                        return d if isinstance(d, dict) else {}
                    except Exception:
                        return {}
                
                test_params_def = safe_parse(task.get('test_params_definition', '{}'))
                
                # 获取默认参数值
                default_params = {}
                for param_name, param_def in test_params_def.items():
                    try:
                        if isinstance(param_def, dict):
                            default_params[param_name] = param_def.get('default')
                        else:
                            # 如果param_def不是字典，它本身就是默认值
                            default_params[param_name] = param_def
                    except Exception as e:
                        print(f"处理参数 {param_name} 时出错: {param_def} (类型: {type(param_def)}), 错误: {e}")
                        # 出错时使用原始值作为默认值
                        default_params[param_name] = param_def
                
                self.parameter_widget.update_parameters(default_params)
                self.current_test_params_def = test_params_def
            
        except Exception as e:
            print(f"加载测试参数失败: {e}")
            self.parameter_widget.update_parameters({})
            self.current_test_params_def = {}
    
    def browse_input_path(self):
        """浏览测试图片路径"""
        path = QFileDialog.getExistingDirectory(self, "选择测试图片文件夹")
        if path:
            self.input_path_edit.setText(path)
    
    def browse_output_path(self):
        """浏览结果输出路径"""
        path = QFileDialog.getExistingDirectory(self, "选择结果输出文件夹")
        if path:
            self.output_path_edit.setText(path)
    
    def edit_parameters(self):
        """编辑测试参数"""
        if not hasattr(self, 'current_test_params_def'):
            QMessageBox.information(self, "提示", "请先选择测试模型")
            return
        
        current_params = self.parameter_widget.get_parameters()
        
        dialog = ParameterEditorDialog(
            self, 
            title="编辑测试参数",
            parameter_definitions=self.current_test_params_def,
            current_values=current_params
        )
        
        dialog.parameters_updated.connect(self.parameter_widget.update_parameters)
        dialog.exec_()
    
    def validate_inputs(self) -> tuple[bool, str]:
        """验证输入参数"""
        if not self.model_combo.currentData():
            return False, "请选择测试模型"
        
        if not self.input_path_edit.text().strip():
            return False, "请选择测试图片路径"
        
        if not os.path.exists(self.input_path_edit.text()):
            return False, "测试图片路径不存在"
        
        # 输出路径是可选的，如果用户选择了，检查是否存在
        user_output_path = self.output_path_edit.text().strip()
        if user_output_path and not os.path.exists(user_output_path):
            return False, "选择的输出路径不存在"
        
        return True, ""
    
    def start_testing(self):
        """开始测试"""
        # 验证输入
        is_valid, error_msg = self.validate_inputs()
        if not is_valid:
            QMessageBox.warning(self, "输入错误", error_msg)
            return
        
        try:
            # 获取模型和任务配置
            model_config = self.model_combo.currentData()
            task_config = self.db_manager.get_task_by_id(model_config['task_id'])
            
            if not task_config:
                QMessageBox.warning(self, "错误", "无法找到对应的任务配置")
                return
            
            # 清除之前的日志
            self.log_text.clear()
            
            # 更新UI状态 - 参考训练对话框
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("测试中...")
            self.status_label.setStyleSheet("font-weight: bold; color: #ff6600;")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            self.log_text.appendPlainText(f"开始测试模型: {model_config['name']}")
            self.log_text.appendPlainText(f"任务类型: {model_config.get('task_type', 'unknown')}")
            self.log_text.appendPlainText(f"测试图片: {self.input_path_edit.text()}")
            self.log_text.appendPlainText("[DEBUG] 按钮状态已设置：开始测试按钮禁用，停止测试按钮启用")
            self.log_text.appendPlainText("-" * 50)
            
            # 启动测试进程 - 参考训练对话框的方式
            self.start_testing_process(task_config, model_config)
            
        except Exception as e:
            self.testing_failed(f"启动测试失败: {str(e)}")
    
    def start_testing_process(self, task_config: Dict[str, Any], model_config: Dict[str, Any]):
        """启动测试进程 - 参考训练对话框的实现"""
        try:
            # 创建进程 - 与训练对话框相同的方式
            self.testing_process = QProcess(self)
            self.testing_process.readyReadStandardOutput.connect(self.read_stdout)
            self.testing_process.readyReadStandardError.connect(self.read_stderr)
            self.testing_process.finished.connect(self.testing_process_finished)
            
            # 准备命令和参数
            python_env = task_config.get('python_env_path', 'python')
            test_script = task_config.get('test_script_path', '')
            
            if not test_script or not os.path.exists(test_script):
                raise Exception(f"测试脚本不存在: {test_script}")
            
            # 环境已验证正常，使用真正的测试脚本
            self.log_text.appendPlainText("[INFO] 使用YOLO测试脚本进行模型测试...")
            
            # 确定输出路径
            user_output_path = self.output_path_edit.text().strip()
            if user_output_path:
                # 用户选择了输出路径，直接使用
                final_output_path = user_output_path
            else:
                # 用户没有选择，使用模型目录下的test_results，并按批次区分
                model_dir = os.path.dirname(model_config.get("onnx_path", ""))
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                final_output_path = os.path.join(model_dir, "test_results", f"test_{timestamp}")
            
            # 记录最终输出路径，用于后续打开文件夹
            self.final_output_path = final_output_path
            
            # 获取测试参数
            test_params = self.parameter_widget.get_parameters()
            
            # 创建测试配置文件
            test_config_file = self._create_test_config_file(model_config, test_params, final_output_path)
            
            # 构建参数
            args = [
                test_script,
                "--model_path", model_config.get("onnx_path", ""),
                "--test_images", self.input_path_edit.text(),
                "--output_path", final_output_path
            ]
            
            # 添加测试参数（作为JSON字符串）
            if test_params:
                test_params_json = json.dumps(test_params)
                args.extend(["--test_params", test_params_json])
            
            # 启动进程
            self.testing_process.start(python_env, args)
            
            if not self.testing_process.waitForStarted(3000):
                raise Exception("无法启动测试进程")
            
            self.log_text.appendPlainText(f"测试进程已启动: {python_env} {' '.join(args)}")
            
        except Exception as e:
            self.testing_failed(f"启动测试进程失败: {str(e)}")
    
    def read_stdout(self):
        """读取标准输出 - 参考训练对话框"""
        if not self.testing_process:
            return
            
        data = self.testing_process.readAllStandardOutput()
        text = data.data().decode('utf-8', errors='ignore')
        
        for line in text.strip().split('\n'):
            if line.strip():
                self.process_output_line(line.strip())
                
    def read_stderr(self):
        """读取标准错误 - 参考训练对话框"""
        if not self.testing_process:
            return
            
        data = self.testing_process.readAllStandardError()
        text = data.data().decode('utf-8', errors='ignore')
        
        for line in text.strip().split('\n'):
            if line.strip():
                self.log_text.appendPlainText(f"[ERROR] {line.strip()}")
    
    def process_output_line(self, line: str):
        """处理输出行 - 参考训练对话框"""
        try:
            # 尝试解析JSON格式的测试结果
            if line.startswith('{') and line.endswith('}'):
                data = json.loads(line)
                
                # 检查是否是测试结果
                if 'status' in data:
                    status = data.get('status', '')
                    if status == 'success':
                        self.testing_completed(data)
                        return
                    elif status == 'error':
                        self.testing_failed(data.get('error_message', '测试失败'))
                        return
                
                # 检查是否是进度信息
                msg_type = data.get('type', '')
                if msg_type == 'progress':
                    progress = data.get('value', 0)
                    message = data.get('message', '')
                    self.progress_bar.setValue(int(progress))
                    if message:
                        self.log_text.appendPlainText(f"[进度] {message}")
                        
                elif msg_type == 'log':
                    message = data.get('message', '')
                    self.log_text.appendPlainText(f"[日志] {message}")
                    
                elif msg_type == 'result':
                    status = data.get('status', '')
                    if status == 'completed':
                        self.testing_completed(data)
                    elif status == 'failed':
                        self.testing_failed(data.get('message', '测试失败'))
                        
                elif msg_type == 'error':
                    error_msg = data.get('message', '未知错误')
                    self.testing_failed(error_msg)
                    
            else:
                # 普通文本输出
                self.log_text.appendPlainText(line)
                
        except json.JSONDecodeError:
            # 不是JSON格式，直接显示
            self.log_text.appendPlainText(line)
    
    def testing_completed(self, result_data: Dict[str, Any]):
        """测试完成"""
        self.log_text.appendPlainText("[JSON] 收到测试完成消息")
        if result_data.get('metrics'):
            self.log_text.appendPlainText(f"[JSON] 测试指标: {result_data['metrics']}")
        
        # 更新进度条
        self.progress_bar.setValue(100)
    
    def testing_failed(self, error_message: str):
        """测试失败"""
        self.status_label.setText("测试失败")
        self.status_label.setStyleSheet("font-weight: bold; color: #cc0000;")
        self.log_text.appendPlainText(f"[错误] {error_message}")
        
        # 重置UI状态
        self.reset_ui_state()
        
        # 标记已经处理过错误，避免重复显示对话框
        self._error_handled = True
        
        # 显示错误提示
        QMessageBox.critical(self, "测试失败", error_message)
    
    def testing_process_finished(self, exit_code, exit_status):
        """测试进程结束处理 - 参考训练对话框"""
        try:
            if exit_code == 0:
                self.log_text.appendPlainText("测试进程正常结束")
                self.status_label.setText("测试完成")
                self.status_label.setStyleSheet("font-weight: bold; color: #00aa00;")
                self.progress_bar.setValue(100)
                
                # 获取实际的输出路径
                actual_output_path = getattr(self, 'final_output_path', self.output_path_edit.text())
                
                # 先显示完成消息
                QMessageBox.information(self, "测试完成", f"模型测试完成！\n\n结果已保存到：\n{actual_output_path}")
                
                # 然后询问是否打开结果文件夹
                reply = QMessageBox.question(
                    self, "打开结果文件夹", 
                    "是否打开结果文件夹查看测试结果？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                
                if reply == QMessageBox.Yes:
                    self.open_result_folder(actual_output_path)
                    
            else:
                self.log_text.appendPlainText(f"测试进程异常结束，退出码: {exit_code}")
                self.status_label.setText("测试失败")
                self.status_label.setStyleSheet("font-weight: bold; color: #cc0000;")
                
                # 只有在没有处理过错误时才显示错误对话框
                if not hasattr(self, '_error_handled') or not self._error_handled:
                    QMessageBox.warning(self, "测试异常", f"测试进程异常结束！\n退出码: {exit_code}")
                
        except Exception as e:
            self.log_text.appendPlainText(f"处理测试结束事件失败: {str(e)}")
        finally:
            # 重置UI状态和错误处理标记
            self.reset_ui_state()
            if hasattr(self, '_error_handled'):
                delattr(self, '_error_handled')
    
    def open_result_folder(self, folder_path: str):
        """打开结果文件夹"""
        try:
            import subprocess
            import platform
            
            if not os.path.exists(folder_path):
                QMessageBox.warning(self, "错误", f"结果文件夹不存在：{folder_path}")
                return
            
            system = platform.system()
            if system == "Windows":
                os.startfile(folder_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])
                
            self.log_text.appendPlainText(f"[INFO] 已打开结果文件夹: {folder_path}")
            
        except Exception as e:
            self.log_text.appendPlainText(f"[ERROR] 打开结果文件夹失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"无法打开结果文件夹：{str(e)}")
    
    def reset_ui_state(self):
        """重置UI状态 - 参考训练对话框"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        if self.testing_process:
            self.testing_process.deleteLater()
            self.testing_process = None
    
    def stop_testing(self):
        """停止测试 - 参考训练对话框"""
        if not self.testing_process:
            return
            
        try:
            self.log_text.appendPlainText("正在停止测试...")
            self.testing_process.terminate()
            
            # 等待进程结束
            if not self.testing_process.waitForFinished(5000):
                self.testing_process.kill()
                self.log_text.appendPlainText("强制终止测试进程")
            else:
                self.log_text.appendPlainText("测试已停止")
                
            self.status_label.setText("已停止")
            self.status_label.setStyleSheet("font-weight: bold; color: #666;")
            
        except Exception as e:
            # 记录错误到日志，避免弹窗干扰用户
            self.log_text.appendPlainText(f"[错误] 停止测试失败: {str(e)}")
        finally:
            self.reset_ui_state()
    
    def _safe_filename(self, name: str) -> str:
        """生成安全的文件名，处理中文和特殊字符"""
        import re
        
        # 移除或替换不安全的字符
        # 保留中文字符、英文字母、数字、下划线、连字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', name)  # 替换Windows不允许的字符
        safe_name = re.sub(r'\s+', '_', safe_name)  # 替换空格为下划线
        safe_name = re.sub(r'_{2,}', '_', safe_name)  # 多个下划线合并为一个
        safe_name = safe_name.strip('_')  # 移除首尾下划线
        
        # 如果处理后为空或过长，使用时间戳
        if not safe_name or len(safe_name) > 100:
            safe_name = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        return safe_name
    
    def _create_test_config_file(self, model_config: Dict[str, Any], test_params: Dict[str, Any], output_path: str) -> str:
        """创建测试配置文件"""
        config_dir = "AI_ENV/temp"
        os.makedirs(config_dir, exist_ok=True)
        
        # 使用模型名称作为配置文件名
        model_name = model_config.get('name', 'unknown_model')
        safe_model_name = self._safe_filename(model_name)
        config_file = os.path.join(config_dir, f"test_config_{safe_model_name}.json")
        
        # 如果文件已存在，添加时间戳避免冲突
        if os.path.exists(config_file):
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            config_file = os.path.join(config_dir, f"test_config_{safe_model_name}_{timestamp}.json")
        
        # 构建测试配置数据
        config_data = {
            "model_id": model_config.get('id'),
            "model_name": model_config.get('name'),
            "task_type": model_config.get('task_type'),
            "model_paths": {
                "onnx_path": model_config.get("onnx_path", ""),
                "tensorrt_path": model_config.get("tensorrt_path", ""),
                "config_path": model_config.get("config_path", "")
            },
            "test_data": {
                "input_path": self.input_path_edit.text(),
                "output_path": output_path
            },
            "test_params": test_params,
            "created_time": datetime.now().isoformat()
        }
        
        # 保存配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        self.log_text.appendPlainText(f"[DEBUG] 测试配置文件已创建: {config_file}")
        return config_file
    
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if self.testing_process and self.testing_process.state() == QProcess.Running:
            reply = QMessageBox.question(
                self, "确认关闭", 
                "测试正在进行中，确定要关闭窗口吗？\n关闭窗口不会停止测试进程。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                event.ignore()
                return
        
        event.accept()