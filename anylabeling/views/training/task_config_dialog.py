"""
任务配置管理对话框
"""
import json
import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLabel, QLineEdit, QPushButton, QFileDialog, QTabWidget,
    QWidget, QTextEdit, QMessageBox, QComboBox, QListWidget,
    QListWidgetItem, QSplitter
)
from PyQt5.QtCore import Qt

from anylabeling.utils.db_manager import DatabaseManager


class TaskConfigDialog(QDialog):
    """任务配置管理对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.current_task = None
        
        self.setWindowTitle("任务配置管理")
        self.setModal(False)  # 改为非模态窗口
        self.resize(1000, 700)
        
        # 设置窗口标志，创建独立的顶级窗口
        from PyQt5.QtCore import Qt
        self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        
        self.setup_ui()
        self.load_tasks()
    
    def setup_ui(self):
        main_layout = QHBoxLayout(self)
        
        # 左侧任务列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 任务列表
        tasks_group = QGroupBox("任务列表")
        tasks_layout = QVBoxLayout(tasks_group)
        
        self.task_list = QListWidget()
        self.task_list.currentItemChanged.connect(self.on_task_selected)
        tasks_layout.addWidget(self.task_list)
        
        # 任务操作按钮
        task_buttons = QHBoxLayout()
        add_btn = QPushButton("添加任务")
        add_btn.clicked.connect(self.add_task)
        delete_btn = QPushButton("删除任务")
        delete_btn.clicked.connect(self.delete_task)
        task_buttons.addWidget(add_btn)
        task_buttons.addWidget(delete_btn)
        tasks_layout.addLayout(task_buttons)
        
        left_layout.addWidget(tasks_group)
        left_panel.setMaximumWidth(300)
        
        # 右侧任务详情
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 基本信息
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        
        self.name_edit = QLineEdit()
        self.task_type_combo = QComboBox()
        self.task_type_combo.setEditable(True)  # 允许编辑
        self.task_type_combo.addItems([
            "object_detection",
            "semantic_segmentation", 
            "instance_segmentation",
            "cell_segmentation"
        ])
        self.python_env_edit = QLineEdit()
        self.train_script_edit = QLineEdit()
        self.test_script_edit = QLineEdit()
        self.output_dir_edit = QLineEdit()
        
        # 添加浏览按钮
        python_env_layout = QHBoxLayout()
        python_env_layout.addWidget(self.python_env_edit)
        python_env_btn = QPushButton("浏览")
        python_env_btn.clicked.connect(lambda: self.browse_file(self.python_env_edit, "选择Python环境", "可执行文件 (*.exe)"))
        python_env_layout.addWidget(python_env_btn)
        
        train_script_layout = QHBoxLayout()
        train_script_layout.addWidget(self.train_script_edit)
        train_script_btn = QPushButton("浏览")
        train_script_btn.clicked.connect(lambda: self.browse_file(self.train_script_edit, "选择训练脚本", "Python文件 (*.py)"))
        train_script_layout.addWidget(train_script_btn)
        
        test_script_layout = QHBoxLayout()
        test_script_layout.addWidget(self.test_script_edit)
        test_script_btn = QPushButton("浏览")
        test_script_btn.clicked.connect(lambda: self.browse_file(self.test_script_edit, "选择测试脚本", "Python文件 (*.py)"))
        test_script_layout.addWidget(test_script_btn)
        
        output_dir_layout = QHBoxLayout()
        output_dir_layout.addWidget(self.output_dir_edit)
        output_dir_btn = QPushButton("浏览")
        output_dir_btn.clicked.connect(lambda: self.browse_directory(self.output_dir_edit, "选择输出目录"))
        output_dir_layout.addWidget(output_dir_btn)
        
        basic_layout.addRow("任务名称:", self.name_edit)
        basic_layout.addRow("任务类型:", self.task_type_combo)
        basic_layout.addRow("Python环境:", python_env_layout)
        basic_layout.addRow("训练脚本:", train_script_layout)
        basic_layout.addRow("测试脚本:", test_script_layout)
        basic_layout.addRow("输出目录:", output_dir_layout)
        
        right_layout.addWidget(basic_group)
        
        # 参数配置选项卡
        params_tab = QTabWidget()
        
        # 训练参数
        train_params_widget = QWidget()
        train_params_layout = QVBoxLayout(train_params_widget)
        train_params_layout.addWidget(QLabel("训练参数 (JSON格式):"))
        self.train_params_edit = QTextEdit()
        self.train_params_edit.setPlainText('{\n  "epochs": 100,\n  "batch": 8,\n  "lr0": 0.001\n}')
        train_params_layout.addWidget(self.train_params_edit)
        params_tab.addTab(train_params_widget, "训练参数")
        
        # 测试参数
        test_params_widget = QWidget()
        test_params_layout = QVBoxLayout(test_params_widget)
        test_params_layout.addWidget(QLabel("测试参数 (JSON格式):"))
        self.test_params_edit = QTextEdit()
        self.test_params_edit.setPlainText('{\n  "conf_threshold": 0.5,\n  "iou_threshold": 0.5\n}')
        test_params_layout.addWidget(self.test_params_edit)
        params_tab.addTab(test_params_widget, "测试参数")
        
        right_layout.addWidget(params_tab)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        save_btn = QPushButton("保存任务")
        save_btn.clicked.connect(self.save_task)
        cancel_btn = QPushButton("关闭")
        cancel_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)
        
        right_layout.addLayout(button_layout)
        
        # 添加到主布局
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setStretchFactor(0, 0)
        splitter.setStretchFactor(1, 1)
        
        main_layout.addWidget(splitter)
    
    def browse_file(self, line_edit, title, file_filter):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, title, "", file_filter)
        if file_path:
            line_edit.setText(file_path)
    
    def browse_directory(self, line_edit, title):
        """浏览目录"""
        dir_path = QFileDialog.getExistingDirectory(self, title)
        if dir_path:
            line_edit.setText(dir_path)
    
    def load_tasks(self):
        """加载任务列表"""
        try:
            tasks = self.db_manager.get_all_tasks()
            self.task_list.clear()
            
            for task in tasks:
                item = QListWidgetItem(task['name'])
                item.setData(Qt.UserRole, task)
                self.task_list.addItem(item)
                
        except Exception as e:
            QMessageBox.critical(self, "加载失败", f"加载任务列表失败: {e}")
    
    def on_task_selected(self, current, previous):
        """任务选择变化"""
        if current is None:
            self.clear_form()
            return
        
        task = current.data(Qt.UserRole)
        if task:
            self.load_task_details(task)
    
    def load_task_details(self, task: dict):
        """加载任务详情"""
        try:
            self.current_task = task
            
            # 填充基本信息
            self.name_edit.setText(task.get('name', ''))
            
            index = self.task_type_combo.findText(task.get('task_type', 'object_detection'))
            if index >= 0:
                self.task_type_combo.setCurrentIndex(index)
            
            self.python_env_edit.setText(task.get('python_env_path', ''))
            self.train_script_edit.setText(task.get('train_script_path', ''))
            self.test_script_edit.setText(task.get('test_script_path', ''))
            self.output_dir_edit.setText(task.get('output_dir', ''))
            
            # 填充参数配置
            train_params = task.get('train_params_definition', {})
            if isinstance(train_params, dict):
                train_params_json = json.dumps(train_params, indent=2, ensure_ascii=False)
            else:
                train_params_json = '{\n  "epochs": 100,\n  "batch": 8,\n  "lr0": 0.001\n}'
            self.train_params_edit.setPlainText(train_params_json)
            
            test_params = task.get('test_params_definition', {})
            if isinstance(test_params, dict):
                test_params_json = json.dumps(test_params, indent=2, ensure_ascii=False)
            else:
                test_params_json = '{\n  "conf_threshold": 0.5,\n  "iou_threshold": 0.5\n}'
            self.test_params_edit.setPlainText(test_params_json)
            
        except Exception as e:
            QMessageBox.critical(self, "加载失败", f"加载任务详情失败: {e}")
    
    def clear_form(self):
        """清空表单"""
        self.current_task = None
        self.name_edit.clear()
        self.task_type_combo.setCurrentIndex(0)
        self.python_env_edit.clear()
        self.train_script_edit.clear()
        self.test_script_edit.clear()
        self.output_dir_edit.clear()
        self.train_params_edit.setPlainText('{\n  "epochs": 100,\n  "batch": 8,\n  "lr0": 0.001\n}')
        self.test_params_edit.setPlainText('{\n  "conf_threshold": 0.5,\n  "iou_threshold": 0.5\n}')
    
    def add_task(self):
        """添加任务"""
        self.clear_form()
        # 设置默认值
        self.python_env_edit.setText("D:/anylabeling2/AI_ENV/python_env/python.exe")
        self.output_dir_edit.setText("AI_ENV/output/new_task")
    
    def delete_task(self):
        """删除任务"""
        current_item = self.task_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "选择任务", "请先选择要删除的任务")
            return
        
        task = current_item.data(Qt.UserRole)
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除任务 '{task['name']}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                success = self.db_manager.delete_task(task['id'])
                if success:
                    QMessageBox.information(self, "删除成功", "任务已删除")
                    self.load_tasks()
                    self.clear_form()
                else:
                    QMessageBox.warning(self, "删除失败", "删除任务失败")
            except Exception as e:
                QMessageBox.critical(self, "删除失败", f"删除任务失败: {e}")
    
    def save_task(self):
        """保存任务"""
        try:
            # 验证必填字段
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "验证失败", "请输入任务名称")
                return
            
            train_script = self.train_script_edit.text().strip()
            if not train_script:
                QMessageBox.warning(self, "验证失败", "请输入训练脚本路径")
                return
            
            # 验证JSON格式
            try:
                train_params = json.loads(self.train_params_edit.toPlainText())
            except json.JSONDecodeError as e:
                QMessageBox.warning(self, "JSON错误", f"训练参数JSON格式错误: {e}")
                return
            
            try:
                test_params = json.loads(self.test_params_edit.toPlainText())
            except json.JSONDecodeError as e:
                QMessageBox.warning(self, "JSON错误", f"测试参数JSON格式错误: {e}")
                return
            
            # 构建任务数据
            task_data = {
                'name': name,
                'task_type': self.task_type_combo.currentText(),
                'python_env_path': self.python_env_edit.text().strip(),
                'train_script_path': train_script,
                'test_script_path': self.test_script_edit.text().strip(),
                'output_dir': self.output_dir_edit.text().strip(),
                'description': f"{self.task_type_combo.currentText()}任务",
                'train_params_definition': train_params,
                'test_params_definition': test_params
            }
            
            # 保存任务
            if self.current_task and self.current_task.get('name') == name:
                # 更新现有任务
                success = self.db_manager.update_task(self.current_task['id'], task_data)
                if success:
                    QMessageBox.information(self, "保存成功", "任务已更新")
                else:
                    QMessageBox.warning(self, "保存失败", "更新任务失败")
            else:
                # 创建新任务
                task_id = self.db_manager.create_task(task_data)
                if task_id:
                    QMessageBox.information(self, "保存成功", "任务已创建")
                else:
                    QMessageBox.warning(self, "保存失败", "创建任务失败")
            
            # 重新加载任务列表
            self.load_tasks()
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存任务失败: {e}")


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    dialog = TaskConfigDialog()
    dialog.show()
    sys.exit(app.exec_())