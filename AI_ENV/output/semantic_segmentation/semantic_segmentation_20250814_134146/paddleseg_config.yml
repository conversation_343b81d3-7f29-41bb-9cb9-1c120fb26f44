batch_size: 8
iters: 30
loss:
  coef:
  - 1
  - 1
  - 1
  types:
  - type: OhemCrossEntropyLoss
  - type: OhemCrossEntropyLoss
  - type: OhemCrossEntropyLoss
lr_scheduler:
  end_lr: 0
  learning_rate: 0.01
  power: 0.9
  type: PolynomialDecay
  warmup_iters: 200
  warmup_start_lr: 1.0e-05
model:
  backbone:
    pretrained: https://bj.bcebos.com/paddleseg/dygraph/PP_STDCNet2.tar.gz
    type: STDC2
  num_classes: 3
  pretrained: null
  type: PPLiteSeg
optimizer:
  momentum: 0.9
  type: sgd
  weight_decay: 0.0005
train_dataset:
  dataset_root: D:/anylabeling2/AI_ENV/output/semantic_segmentation/semantic_segmentation_20250814_134146/dataset
  mode: train
  num_classes: 3
  train_path: D:/anylabeling2/AI_ENV/output/semantic_segmentation/semantic_segmentation_20250814_134146/dataset/train_list.txt
  transforms:
  - max_scale_factor: 2.0
    min_scale_factor: 0.5
    scale_step_size: 0.25
    type: ResizeStepScaling
  - crop_size:
    - 512
    - 512
    type: RandomPaddingCrop
  - type: RandomHorizontalFlip
  - brightness_range: 0.5
    contrast_range: 0.5
    hue_range: 0.5
    saturation_range: 0.5
    type: RandomDistort
  - type: Normalize
  type: Dataset
val_dataset:
  dataset_root: D:/anylabeling2/AI_ENV/output/semantic_segmentation/semantic_segmentation_20250814_134146/dataset
  mode: val
  num_classes: 3
  transforms:
  - type: Normalize
  type: Dataset
  val_path: D:/anylabeling2/AI_ENV/output/semantic_segmentation/semantic_segmentation_20250814_134146/dataset/val_list.txt
