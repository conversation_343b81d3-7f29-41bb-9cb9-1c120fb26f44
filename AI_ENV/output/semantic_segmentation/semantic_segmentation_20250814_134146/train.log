2025-08-14 13:44:35.460 | INFO     | __main__:_log:157 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:44:35.461 | INFO     | __main__:_log:157 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:44:35.462 | INFO     | __main__:_log:157 - 数据集ID: 18
2025-08-14 13:44:35.462 | INFO     | __main__:_log:157 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:44:35.463 | INFO     | __main__:_log:157 - 获取到 35 个标注记录
2025-08-14 13:44:35.497 | INFO     | __main__:_log:157 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:44:35.742 | INFO     | __main__:_log:157 - 有效样本: 35, 失败样本: 0
2025-08-14 13:44:35.743 | INFO     | __main__:_log:157 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:44:35.748 | INFO     | __main__:_log:157 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:44:35.748 | INFO     | __main__:_log:157 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:44:35.749 | INFO     | __main__:_log:157 - 模型训练失败: module 'paddleseg.utils.utils' has no attribute 'Config'
2025-08-14 13:44:35.749 | INFO     | __main__:_log:157 - 训练流程失败: module 'paddleseg.utils.utils' has no attribute 'Config'
2025-08-14 13:45:31.754 | INFO     | __main__:_log:158 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:45:31.754 | INFO     | __main__:_log:158 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:45:31.755 | INFO     | __main__:_log:158 - 数据集ID: 18
2025-08-14 13:45:31.755 | INFO     | __main__:_log:158 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:45:31.756 | INFO     | __main__:_log:158 - 获取到 35 个标注记录
2025-08-14 13:45:31.791 | INFO     | __main__:_log:158 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:45:32.030 | INFO     | __main__:_log:158 - 有效样本: 35, 失败样本: 0
2025-08-14 13:45:32.030 | INFO     | __main__:_log:158 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:45:32.034 | INFO     | __main__:_log:158 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:45:32.035 | INFO     | __main__:_log:158 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:45:32.040 | INFO     | __main__:_log:158 - 模型训练失败: 'Config' object has no attribute 'model'
2025-08-14 13:45:32.040 | INFO     | __main__:_log:158 - 训练流程失败: 'Config' object has no attribute 'model'
2025-08-14 13:46:38.798 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:46:38.798 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:46:38.798 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:46:38.799 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:46:38.800 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:46:38.834 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:46:39.074 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:46:39.074 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:46:39.079 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:46:39.079 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:47:13.080 | INFO     | __main__:_log:159 - 模型训练失败: Tried to create a Dataset object, but the operation has failed. Please double check the arguments used to create the object.
The error message is: 
`train_path` is not found: train_list.txt
2025-08-14 13:47:13.081 | INFO     | __main__:_log:159 - 训练流程失败: Tried to create a Dataset object, but the operation has failed. Please double check the arguments used to create the object.
The error message is: 
`train_path` is not found: train_list.txt
2025-08-14 13:48:04.946 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:48:04.947 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:48:04.947 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:48:04.947 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:48:04.949 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:48:04.992 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:48:05.272 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:48:05.273 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:48:05.280 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:48:05.280 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:48:06.362 | INFO     | __main__:_log:159 - 模型训练失败: Tried to create a Dataset object, but the operation has failed. Please double check the arguments used to create the object.
The error message is: 
`train_path` is not found: train_list.txt
2025-08-14 13:48:06.362 | INFO     | __main__:_log:159 - 训练流程失败: Tried to create a Dataset object, but the operation has failed. Please double check the arguments used to create the object.
The error message is: 
`train_path` is not found: train_list.txt
2025-08-14 13:48:51.549 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:48:51.550 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:48:51.550 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:48:51.550 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:48:51.551 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:48:51.585 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:48:51.819 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:48:51.820 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:48:51.824 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:48:51.825 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:48:52.856 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:48:52.856 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:48:52.857 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:48:52.857 | INFO     | __main__:_log:159 - 模型训练失败: 'Config' object has no attribute 'get'
2025-08-14 13:48:52.857 | INFO     | __main__:_log:159 - 训练流程失败: 'Config' object has no attribute 'get'
2025-08-14 13:49:22.488 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:49:22.488 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:49:22.488 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:49:22.489 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:49:22.490 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:49:22.525 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:49:22.773 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:49:22.774 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:49:22.780 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:49:22.780 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:49:23.845 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:49:23.845 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:49:23.845 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:49:23.845 | INFO     | __main__:_log:159 - 模型训练失败: train() got an unexpected keyword argument 'fp16'
2025-08-14 13:49:23.846 | INFO     | __main__:_log:159 - 训练流程失败: train() got an unexpected keyword argument 'fp16'
2025-08-14 13:50:02.051 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:50:02.051 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:50:02.052 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:50:02.052 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:50:02.053 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:50:02.087 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:50:02.327 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:50:02.327 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:50:02.332 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:50:02.333 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:50:03.618 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:50:03.618 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:50:03.618 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:50:16.128 | INFO     | __main__:_log:159 - 模型训练失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

2025-08-14 13:50:16.128 | INFO     | __main__:_log:159 - 训练流程失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

