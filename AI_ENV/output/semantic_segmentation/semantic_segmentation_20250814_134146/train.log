2025-08-14 13:44:35.460 | INFO     | __main__:_log:157 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:44:35.461 | INFO     | __main__:_log:157 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:44:35.462 | INFO     | __main__:_log:157 - 数据集ID: 18
2025-08-14 13:44:35.462 | INFO     | __main__:_log:157 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:44:35.463 | INFO     | __main__:_log:157 - 获取到 35 个标注记录
2025-08-14 13:44:35.497 | INFO     | __main__:_log:157 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:44:35.742 | INFO     | __main__:_log:157 - 有效样本: 35, 失败样本: 0
2025-08-14 13:44:35.743 | INFO     | __main__:_log:157 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:44:35.748 | INFO     | __main__:_log:157 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:44:35.748 | INFO     | __main__:_log:157 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:44:35.749 | INFO     | __main__:_log:157 - 模型训练失败: module 'paddleseg.utils.utils' has no attribute 'Config'
2025-08-14 13:44:35.749 | INFO     | __main__:_log:157 - 训练流程失败: module 'paddleseg.utils.utils' has no attribute 'Config'
2025-08-14 13:45:31.754 | INFO     | __main__:_log:158 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:45:31.754 | INFO     | __main__:_log:158 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:45:31.755 | INFO     | __main__:_log:158 - 数据集ID: 18
2025-08-14 13:45:31.755 | INFO     | __main__:_log:158 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:45:31.756 | INFO     | __main__:_log:158 - 获取到 35 个标注记录
2025-08-14 13:45:31.791 | INFO     | __main__:_log:158 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:45:32.030 | INFO     | __main__:_log:158 - 有效样本: 35, 失败样本: 0
2025-08-14 13:45:32.030 | INFO     | __main__:_log:158 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:45:32.034 | INFO     | __main__:_log:158 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:45:32.035 | INFO     | __main__:_log:158 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:45:32.040 | INFO     | __main__:_log:158 - 模型训练失败: 'Config' object has no attribute 'model'
2025-08-14 13:45:32.040 | INFO     | __main__:_log:158 - 训练流程失败: 'Config' object has no attribute 'model'
2025-08-14 13:46:38.798 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:46:38.798 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:46:38.798 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:46:38.799 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:46:38.800 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:46:38.834 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:46:39.074 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:46:39.074 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:46:39.079 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:46:39.079 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:47:13.080 | INFO     | __main__:_log:159 - 模型训练失败: Tried to create a Dataset object, but the operation has failed. Please double check the arguments used to create the object.
The error message is: 
`train_path` is not found: train_list.txt
2025-08-14 13:47:13.081 | INFO     | __main__:_log:159 - 训练流程失败: Tried to create a Dataset object, but the operation has failed. Please double check the arguments used to create the object.
The error message is: 
`train_path` is not found: train_list.txt
2025-08-14 13:48:04.946 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:48:04.947 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:48:04.947 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:48:04.947 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:48:04.949 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:48:04.992 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:48:05.272 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:48:05.273 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:48:05.280 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:48:05.280 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:48:06.362 | INFO     | __main__:_log:159 - 模型训练失败: Tried to create a Dataset object, but the operation has failed. Please double check the arguments used to create the object.
The error message is: 
`train_path` is not found: train_list.txt
2025-08-14 13:48:06.362 | INFO     | __main__:_log:159 - 训练流程失败: Tried to create a Dataset object, but the operation has failed. Please double check the arguments used to create the object.
The error message is: 
`train_path` is not found: train_list.txt
2025-08-14 13:48:51.549 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:48:51.550 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:48:51.550 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:48:51.550 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:48:51.551 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:48:51.585 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:48:51.819 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:48:51.820 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:48:51.824 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:48:51.825 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:48:52.856 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:48:52.856 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:48:52.857 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:48:52.857 | INFO     | __main__:_log:159 - 模型训练失败: 'Config' object has no attribute 'get'
2025-08-14 13:48:52.857 | INFO     | __main__:_log:159 - 训练流程失败: 'Config' object has no attribute 'get'
2025-08-14 13:49:22.488 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:49:22.488 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:49:22.488 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:49:22.489 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:49:22.490 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:49:22.525 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:49:22.773 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:49:22.774 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:49:22.780 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:49:22.780 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:49:23.845 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:49:23.845 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:49:23.845 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:49:23.845 | INFO     | __main__:_log:159 - 模型训练失败: train() got an unexpected keyword argument 'fp16'
2025-08-14 13:49:23.846 | INFO     | __main__:_log:159 - 训练流程失败: train() got an unexpected keyword argument 'fp16'
2025-08-14 13:50:02.051 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:50:02.051 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:50:02.052 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:50:02.052 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:50:02.053 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:50:02.087 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:50:02.327 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:50:02.327 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:50:02.332 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:50:02.333 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:50:03.618 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:50:03.618 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:50:03.618 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:50:16.128 | INFO     | __main__:_log:159 - 模型训练失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

2025-08-14 13:50:16.128 | INFO     | __main__:_log:159 - 训练流程失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

2025-08-14 13:52:04.581 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:52:04.583 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:52:04.583 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:52:04.583 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:52:04.584 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:52:04.625 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:52:04.923 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:52:04.924 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:52:04.929 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:52:04.930 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:52:06.044 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:52:06.045 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:52:06.045 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:52:14.819 | INFO     | __main__:_log:159 - 模型训练失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

2025-08-14 13:52:14.820 | INFO     | __main__:_log:159 - 训练流程失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

2025-08-14 13:52:52.780 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:52:52.780 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:52:52.781 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:52:52.781 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:52:52.783 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:52:52.816 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:52:53.055 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:52:53.056 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:52:53.063 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:52:53.063 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:52:54.110 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:52:54.111 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:52:54.111 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:53:02.856 | INFO     | __main__:_log:159 - 模型训练失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

2025-08-14 13:53:02.857 | INFO     | __main__:_log:159 - 训练流程失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

2025-08-14 13:53:48.730 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:53:48.730 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:53:48.730 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:53:48.731 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:53:48.732 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:53:48.766 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:53:49.014 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:53:49.015 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:53:49.019 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:53:49.019 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:53:50.051 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:53:50.052 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:53:50.052 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:53:50.052 | INFO     | __main__:_log:159 - 验证集样本不足(7个)，跳过训练中的验证步骤
2025-08-14 13:54:01.336 | INFO     | __main__:_log:159 - 开始模型评估...
2025-08-14 13:54:01.337 | INFO     | __main__:_log:159 - 模型评估失败: 'function' object has no attribute 'evaluate'
2025-08-14 13:54:01.337 | INFO     | __main__:_log:159 - 跳过评估，使用默认指标
2025-08-14 13:54:01.337 | INFO     | __main__:_log:159 - PaddleSeg语义分割模型训练完成
2025-08-14 13:54:01.338 | INFO     | __main__:_log:159 - 开始导出ONNX模型...
2025-08-14 13:54:01.338 | INFO     | __main__:_log:159 - ONNX导出失败: 找不到训练好的模型文件
2025-08-14 13:54:01.339 | INFO     | __main__:_log:159 - 训练流程失败: 找不到训练好的模型文件
2025-08-14 13:55:00.978 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:55:00.979 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:55:00.979 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:55:00.979 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:55:00.981 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:55:01.046 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:55:01.503 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:55:01.503 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:55:01.511 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:55:01.512 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:55:02.610 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:55:02.611 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:55:02.611 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:55:02.611 | INFO     | __main__:_log:159 - 验证集样本不足(7个)，跳过训练中的验证步骤
2025-08-14 13:55:12.967 | INFO     | __main__:_log:159 - 开始模型评估...
2025-08-14 13:55:13.021 | INFO     | __main__:_log:159 - 模型评估失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

2025-08-14 13:55:13.022 | INFO     | __main__:_log:159 - 跳过评估，使用默认指标
2025-08-14 13:55:13.022 | INFO     | __main__:_log:159 - PaddleSeg语义分割模型训练完成
2025-08-14 13:55:13.022 | INFO     | __main__:_log:159 - 开始导出ONNX模型...
2025-08-14 13:55:13.024 | INFO     | __main__:_log:159 - 找到模型目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\iter_30
2025-08-14 13:55:13.024 | INFO     | __main__:_log:159 - 加载模型: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\iter_30\model
2025-08-14 13:55:13.025 | INFO     | __main__:_log:159 - ONNX导出失败: c_paddle_to_onnx() got an unexpected keyword argument 'input_shape_dict'
2025-08-14 13:55:13.025 | INFO     | __main__:_log:159 - 训练流程失败: c_paddle_to_onnx() got an unexpected keyword argument 'input_shape_dict'
2025-08-14 13:55:45.981 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:55:45.981 | INFO     | __main__:_log:159 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:55:45.982 | INFO     | __main__:_log:159 - 数据集ID: 18
2025-08-14 13:55:45.982 | INFO     | __main__:_log:159 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:55:45.983 | INFO     | __main__:_log:159 - 获取到 35 个标注记录
2025-08-14 13:55:46.024 | INFO     | __main__:_log:159 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:55:46.333 | INFO     | __main__:_log:159 - 有效样本: 35, 失败样本: 0
2025-08-14 13:55:46.334 | INFO     | __main__:_log:159 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:55:46.339 | INFO     | __main__:_log:159 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:55:46.339 | INFO     | __main__:_log:159 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:55:47.406 | INFO     | __main__:_log:159 - 模型: PPLiteSeg
2025-08-14 13:55:47.406 | INFO     | __main__:_log:159 - 训练样本数: 28
2025-08-14 13:55:47.407 | INFO     | __main__:_log:159 - 验证样本数: 7
2025-08-14 13:55:47.407 | INFO     | __main__:_log:159 - 验证集样本不足(7个)，跳过训练中的验证步骤
2025-08-14 13:55:57.640 | INFO     | __main__:_log:159 - 开始模型评估...
2025-08-14 13:55:57.691 | INFO     | __main__:_log:159 - 模型评估失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

2025-08-14 13:55:57.693 | INFO     | __main__:_log:159 - 跳过评估，使用默认指标
2025-08-14 13:55:57.694 | INFO     | __main__:_log:159 - PaddleSeg语义分割模型训练完成
2025-08-14 13:55:57.694 | INFO     | __main__:_log:159 - 开始导出ONNX模型...
2025-08-14 13:55:57.696 | INFO     | __main__:_log:159 - 找到模型目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\iter_30
2025-08-14 13:55:57.697 | INFO     | __main__:_log:159 - 加载模型: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\iter_30\model
2025-08-14 13:55:57.846 | INFO     | __main__:_log:159 - ONNX模型已导出: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\semantic_segmentation_20250814_134146.onnx
2025-08-14 13:55:57.849 | INFO     | __main__:_log:159 - 训练结果已保存
2025-08-14 13:55:57.849 | INFO     | __main__:_log:159 - 模型文件: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:55:57.849 | INFO     | __main__:_log:159 - ONNX文件: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\semantic_segmentation_20250814_134146.onnx
2025-08-14 13:55:57.850 | INFO     | __main__:_log:159 - 配置文件: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\training_config.json
2025-08-14 13:55:57.850 | INFO     | __main__:_log:159 - 指标文件: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\training_metrics.json
2025-08-14 13:55:57.850 | INFO     | __main__:_log:159 - 日志文件: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\training_log.txt
2025-08-14 13:55:57.851 | INFO     | __main__:_log:159 - 训练流程完成！
