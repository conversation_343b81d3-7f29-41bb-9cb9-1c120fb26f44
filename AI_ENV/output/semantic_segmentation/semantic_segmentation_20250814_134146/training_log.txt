2025-08-14 13:55:45 - 开始PaddleSeg语义分割模型训练
2025-08-14 13:55:45 - 模型名称: semantic_segmentation_20250814_134146
2025-08-14 13:55:45 - 数据集ID: 18
2025-08-14 13:55:45 - 输出目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146
2025-08-14 13:55:45 - 获取到 35 个标注记录
2025-08-14 13:55:46 - 提取到标签: ['background', 'dead', 'live']
2025-08-14 13:55:46 - 有效样本: 35, 失败样本: 0
2025-08-14 13:55:46 - 数据集分割: 训练集 28, 验证集 7
2025-08-14 13:55:46 - PaddleSeg配置文件已创建: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\paddleseg_config.yml
2025-08-14 13:55:46 - 开始PaddleSeg语义分割模型训练...
2025-08-14 13:55:47 - 模型: PPLiteSeg
2025-08-14 13:55:47 - 训练样本数: 28
2025-08-14 13:55:47 - 验证样本数: 7
2025-08-14 13:55:47 - 验证集样本不足(7个)，跳过训练中的验证步骤
2025-08-14 13:55:57 - 开始模型评估...
2025-08-14 13:55:57 - 模型评估失败: (InvalidArgument) The axis is expected to be in range of [0, 0), but got 0
  [Hint: Expected axis >= -rank && axis < rank == true, but received axis >= -rank && axis < rank:0 != true:1.] (at ..\paddle\phi\infermeta\multiary.cc:961)

2025-08-14 13:55:57 - 跳过评估，使用默认指标
2025-08-14 13:55:57 - PaddleSeg语义分割模型训练完成
2025-08-14 13:55:57 - 开始导出ONNX模型...
2025-08-14 13:55:57 - 找到模型目录: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\iter_30
2025-08-14 13:55:57 - 加载模型: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\iter_30\model
2025-08-14 13:55:57 - ONNX模型已导出: D:\anylabeling2\AI_ENV\output\semantic_segmentation\semantic_segmentation_20250814_134146\semantic_segmentation_20250814_134146.onnx