__all__ = [
    'astronaut',
    'binary_blobs',
    'brain',
    'brick',
    'camera',
    'cat',
    'cell',
    'cells3d',
    'checkerboard',
    'chelsea',
    'clock',
    'coffee',
    'coins',
    'colorwheel',
    'data_dir',
    'download_all',
    'eagle',
    'file_hash',
    'grass',
    'gravel',
    'horse',
    'hubble_deep_field',
    'human_mitosis',
    'immunohistochemistry',
    'kidney',
    'lbp_frontal_face_cascade_filename',
    'lfw_subset',
    'lily',
    'logo',
    'microaneurysms',
    'moon',
    'nickel_solidification',
    'page',
    'protein_transport',
    'retina',
    'rocket',
    'shepp_logan_phantom',
    'skin',
    'stereo_motorcycle',
    'text',
    'vortex',
]

from ._binary_blobs import binary_blobs
from ._fetchers import (
    astronaut,
    brain,
    brick,
    camera,
    cat,
    cell,
    cells3d,
    checkerboard,
    chelsea,
    clock,
    coffee,
    coins,
    colorwheel,
    data_dir,
    download_all,
    eagle,
    file_hash,
    grass,
    gravel,
    horse,
    hubble_deep_field,
    human_mitosis,
    immunohistochemistry,
    kidney,
    lbp_frontal_face_cascade_filename,
    lfw_subset,
    lily,
    logo,
    microaneurysms,
    moon,
    nickel_solidification,
    page,
    palisades_of_vogt,
    protein_transport,
    retina,
    rocket,
    shepp_logan_phantom,
    skin,
    stereo_motorcycle,
    text,
    vortex,
)
