{"model_name": "semantic_segmentation_20250814_132952", "task_id": 12, "task_type": "semantic_segmentation", "dataset_id": 18, "dataset_path": "D:/anylabeling2/datasets/d", "output_dir": "D:/anylabeling2/AI_ENV/output/semantic_segmentation", "python_env": "D:/anylabeling2/AI_ENV/python_env/python.exe", "train_script": "D:/anylabeling2/AI_ENV/modules/semantic_segmentation/train_semantic_segmentation.py", "train_params": {"batch_size": 8, "epochs": 10, "image_size": 512, "learning_rate": 0.01}}