"""
Cellpose细胞分割训练脚本
基于Cellpose的细胞分割模型训练
"""
import argparse
import json
import os
import sys
import traceback
import shutil
import random
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 直接导入utils.db_manager
from anylabeling.utils.db_manager import DatabaseManager

try:
    from cellpose import models, io, train
    from cellpose.core import use_gpu
    import numpy as np
    import cv2
    from PIL import Image
    import tifffile
    import matplotlib.pyplot as plt
    from skimage import measure
    import torch
except ImportError as e:
    print(f"[ERROR] 缺少必要的依赖包: {e}")
    print("[ERROR] 请安装: pip install cellpose opencv-python pillow tifffile matplotlib scikit-image torch")
    sys.exit(1)


class CellposeTrainer:
    """Cellpose细胞分割训练器"""
    
    def __init__(self, model_name: str, dataset_id: int, train_params: Dict[str, Any]):
        """
        初始化训练器
        
        Args:
            model_name: 模型名称
            dataset_id: 数据集ID
            train_params: 训练参数
        """
        self.model_name = model_name
        self.dataset_id = dataset_id
        self.train_params = train_params
        
        # 初始化数据库管理器
        self.db_manager = DatabaseManager()
        
        # 获取数据集信息
        self.dataset_info = self.db_manager.get_dataset(dataset_id)
        if not self.dataset_info:
            raise ValueError(f"无法获取数据集信息: {dataset_id}")
        
        # 设置输出目录
        self.output_base_dir = Path("D:/anylabeling2/AI_ENV/output/cell_segmentation")
        self.model_output_path = self.output_base_dir / model_name
        self.model_output_path.mkdir(parents=True, exist_ok=True)
        
        # 创建Cellpose数据集目录
        self.cellpose_dataset_path = self.model_output_path / "dataset"
        self.cellpose_dataset_path.mkdir(exist_ok=True)
        
        # 检查GPU可用性
        self.use_gpu = use_gpu()
        
        # 训练结果
        self.training_results = {}
        self.training_log = []
    
    def _log(self, message: str):
        """记录日志"""
        print(f"[INFO] {message}")
        self.training_log.append(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}")
    
    def _get_dataset_annotations(self) -> List[Dict[str, Any]]:
        """获取数据集的所有标注信息"""
        try:
            annotations = self.db_manager.get_annotations_for_dataset(self.dataset_id)
            self._log(f"获取到 {len(annotations)} 个标注记录")
            return annotations
        except Exception as e:
            raise Exception(f"获取数据集标注失败: {e}")
    
    def _create_cellpose_masks(self, label_data: Dict, image_size: Tuple[int, int]) -> np.ndarray:
        """创建Cellpose格式的实例掩码"""
        width, height = image_size
        mask = np.zeros((height, width), dtype=np.uint16)
        
        instance_id = 1
        for shape in label_data.get('shapes', []):
            if shape['shape_type'] not in ['polygon', 'circle', 'rectangle']:
                continue
            
            points = shape['points']
            
            if shape['shape_type'] == 'polygon':
                # 多边形填充
                pts = np.array(points, dtype=np.int32)
                cv2.fillPoly(mask, [pts], instance_id)
            
            elif shape['shape_type'] == 'rectangle':
                # 矩形填充
                x1, y1 = points[0]
                x2, y2 = points[1]
                x1, x2 = int(min(x1, x2)), int(max(x1, x2))
                y1, y2 = int(min(y1, y2)), int(max(y1, y2))
                mask[y1:y2, x1:x2] = instance_id
            
            elif shape['shape_type'] == 'circle':
                # 圆形填充
                (x1, y1), (x2, y2) = points
                center = (int(x1), int(y1))
                radius = int(np.sqrt((x2 - x1)**2 + (y2 - y1)**2))
                cv2.circle(mask, center, radius, instance_id, -1)
            
            instance_id += 1
        
        return mask
    
    def _prepare_cellpose_dataset(self, annotations: List[Dict[str, Any]]) -> Tuple[List[str], List[str]]:
        """准备Cellpose格式的数据集
        
        Cellpose期望的数据集结构:
        dataset/
        ├── train/
        │   ├── image1.tif
        │   ├── image1_masks.tif
        │   ├── image2.tif
        │   ├── image2_masks.tif
        │   └── ...
        └── test/
            ├── image1.tif
            ├── image1_masks.tif
            └── ...
        """
        dataset_dir = self.dataset_info['output_dir']
        labels_dir = os.path.join(dataset_dir, 'labels')
        
        valid_samples = []
        failed_samples = []
        
        # 创建Cellpose数据集目录结构
        train_dir = self.cellpose_dataset_path / 'train'
        test_dir = self.cellpose_dataset_path / 'test'
        train_dir.mkdir(exist_ok=True)
        test_dir.mkdir(exist_ok=True)
        
        for annotation in annotations:
            image_path = annotation['image_path']
            if not os.path.exists(image_path):
                failed_samples.append(f"图片不存在: {image_path}")
                continue
            
            # 读取标注文件
            label_file = os.path.splitext(os.path.basename(image_path))[0] + ".json"
            label_path = os.path.join(labels_dir, label_file)
            
            if not os.path.exists(label_path):
                failed_samples.append(f"标注文件不存在: {label_path}")
                continue
            
            try:
                with open(label_path, 'r', encoding='utf-8') as f:
                    label_data = json.load(f)
                
                # 检查是否有有效的分割标注
                has_valid_annotation = False
                for shape in label_data.get('shapes', []):
                    if shape['shape_type'] in ['polygon', 'circle', 'rectangle']:
                        has_valid_annotation = True
                        break
                
                if has_valid_annotation:
                    # 读取图片
                    #image = cv2.imread(image_path)
                    image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                    if image is None:
                        failed_samples.append(f"无法读取图片: {image_path}")
                        continue
                    
                    # 转换为RGB
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    height, width = image.shape[:2]
                    
                    # 创建实例掩码
                    mask = self._create_cellpose_masks(label_data, (width, height))
                    
                    # 保存图片和掩码
                    image_name = os.path.splitext(os.path.basename(image_path))[0]
                    
                    # 保存为TIFF格式（Cellpose推荐）
                    image_save_path = train_dir / f"{image_name}.tif"
                    mask_save_path = train_dir / f"{image_name}_masks.tif"
                    
                    tifffile.imwrite(str(image_save_path), image)
                    tifffile.imwrite(str(mask_save_path), mask)
                    
                    valid_samples.append(str(image_save_path))
                else:
                    failed_samples.append(f"无有效分割标注: {image_path}")
                    
            except Exception as e:
                failed_samples.append(f"处理标注失败 {label_path}: {e}")
        
        self._log(f"有效样本: {len(valid_samples)}, 失败样本: {len(failed_samples)}")
        if failed_samples:
            self._log(f"失败样本详情: {failed_samples[:5]}...")  # 只显示前5个
        
        return valid_samples, failed_samples
    
    def _split_dataset(self, valid_samples: List[str], train_ratio: float = 0.8) -> Tuple[List[str], List[str]]:
        """分割数据集为训练集和测试集"""
        random.shuffle(valid_samples)
        
        train_size = int(len(valid_samples) * train_ratio)
        train_samples = valid_samples[:train_size]
        test_samples = valid_samples[train_size:]
        
        # 移动测试集文件到test目录
        test_dir = self.cellpose_dataset_path / 'test'
        for sample_path in test_samples:
            sample_file = Path(sample_path)
            mask_file = sample_file.parent / f"{sample_file.stem}_masks.tif"
            
            # 移动图片和掩码文件
            new_image_path = test_dir / sample_file.name
            new_mask_path = test_dir / mask_file.name
            
            shutil.move(str(sample_file), str(new_image_path))
            shutil.move(str(mask_file), str(new_mask_path))
        
        self._log(f"数据集分割: 训练集 {len(train_samples)}, 测试集 {len(test_samples)}")
        return train_samples, test_samples

    def _train_cellpose_model(self, train_samples: List[str]) -> Dict[str, Any]:
        """训练Cellpose模型"""
        try:
            self._log("开始Cellpose细胞分割模型训练...")

            # 获取训练参数
            model_type = self.train_params.get('model_type', 'cyto')  # 'cyto', 'nuclei', 'cyto2'
            n_epochs = self.train_params.get('n_epochs', 100)
            learning_rate = self.train_params.get('learning_rate', 0.1)
            weight_decay = self.train_params.get('weight_decay', 0.0001)
            batch_size = self.train_params.get('batch_size', 8)

            self._log(f"训练参数: 模型类型={model_type}, 轮数={n_epochs}, 学习率={learning_rate}")
            self._log(f"使用GPU: {self.use_gpu}")

            # 准备训练数据
            train_dir = str(self.cellpose_dataset_path / 'train')

            # 获取图片和掩码文件列表
            images, masks, image_names = io.load_train_test_data(
                train_dir, test_dir=None, mask_filter='_masks'
            )

            self._log(f"加载训练数据: {len(images)} 张图片")

            # 创建模型
            model = models.CellposeModel(
                gpu=self.use_gpu,
                model_type=model_type
            )

            # 开始训练
            new_model_path = train.train_seg(
                model.net,
                train_data=images,
                train_labels=masks,
                train_files=image_names,
                test_data=None,
                test_labels=None,
                test_files=None,
                channels=[0, 0],  # 灰度图像
                normalize=True,
                save_path=str(self.model_output_path),
                save_every=50,
                n_epochs=n_epochs,
                learning_rate=learning_rate,
                weight_decay=weight_decay,
                batch_size=batch_size,
                model_name=self.model_name
            )

            # 训练指标
            metrics = {
                'model_path': new_model_path,
                'n_epochs': n_epochs,
                'learning_rate': learning_rate,
                'batch_size': batch_size,
                'train_samples': len(images),
                'model_type': model_type
            }

            self._log("Cellpose模型训练完成")
            self._log(f"模型保存路径: {new_model_path}")

            return metrics

        except Exception as e:
            self._log(f"模型训练失败: {e}")
            raise

    def _export_model(self, model_path: str) -> str:
        """导出模型（Cellpose模型已经是可部署格式）"""
        try:
            self._log("准备模型导出...")

            # Cellpose模型本身就是可部署的格式
            # 复制模型文件到输出目录
            if os.path.exists(model_path):
                export_path = self.model_output_path / f"{self.model_name}_cellpose.model"
                shutil.copy2(model_path, export_path)

                self._log(f"模型已导出: {export_path}")
                return str(export_path)
            else:
                raise FileNotFoundError(f"模型文件不存在: {model_path}")

        except Exception as e:
            self._log(f"模型导出失败: {e}")
            raise

    def _save_training_results(self, metrics: Dict[str, Any], export_path: str) -> Dict[str, Any]:
        """保存训练结果"""
        try:
            # 保存训练配置
            training_config = {
                'model_name': self.model_name,
                'dataset_id': self.dataset_id,
                'training_params': self.train_params,
                'training_time': datetime.now().isoformat(),
                'model_architecture': 'Cellpose',
                'task_type': 'cell_segmentation'
            }

            config_file = self.model_output_path / 'training_config.json'
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(training_config, f, ensure_ascii=False, indent=2)

            # 保存训练指标
            metrics_file = self.model_output_path / 'training_metrics.json'
            with open(metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, ensure_ascii=False, indent=2)

            # 保存训练日志
            log_file = self.model_output_path / 'training_log.txt'
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.training_log))

            self._log("训练结果已保存")
            self._log(f"模型文件: {metrics.get('model_path', '')}")
            self._log(f"导出文件: {export_path}")
            self._log(f"配置文件: {config_file}")
            self._log(f"指标文件: {metrics_file}")
            self._log(f"日志文件: {log_file}")

            return {
                'model_path': metrics.get('model_path', ''),
                'export_path': export_path,
                'config_path': str(config_file),
                'metrics_path': str(metrics_file),
                'log_path': str(log_file),
                'metrics': metrics
            }

        except Exception as e:
            self._log(f"保存训练结果失败: {e}")
            raise

    def train(self) -> Dict[str, Any]:
        """执行完整的训练流程"""
        try:
            self._log(f"开始Cellpose细胞分割模型训练")
            self._log(f"模型名称: {self.model_name}")
            self._log(f"数据集ID: {self.dataset_id}")
            self._log(f"输出目录: {self.model_output_path}")

            # 1. 获取数据集标注
            annotations = self._get_dataset_annotations()
            if not annotations:
                raise Exception("数据集中没有标注数据")

            # 2. 准备Cellpose格式数据集
            valid_samples, failed_samples = self._prepare_cellpose_dataset(annotations)
            if not valid_samples:
                raise Exception("没有有效的训练样本")

            # 3. 分割数据集
            train_ratio = self.train_params.get('train_ratio', 0.8)
            train_samples, test_samples = self._split_dataset(valid_samples, train_ratio)

            # 4. 训练模型
            metrics = self._train_cellpose_model(train_samples)

            # 5. 导出模型
            export_path = self._export_model(metrics['model_path'])

            # 6. 保存结果
            results = self._save_training_results(metrics, export_path)

            # 输出最终结果
            final_output = {
                'status': 'success',
                'model_path': results['model_path'],
                'export_path': results['export_path'],
                'metrics': results['metrics'],
                'train_samples': len(train_samples),
                'test_samples': len(test_samples),
                'failed_samples': len(failed_samples),
                'message': 'Cellpose细胞分割训练完成'
            }

            self._log("训练流程完成！")
            return final_output

        except Exception as e:
            self._log(f"训练流程失败: {e}")

            # 输出错误结果
            error_output = {
                'status': 'error',
                'error_message': str(e),
                'model_path': '',
                'export_path': '',
                'metrics': {},
                'message': 'Cellpose细胞分割训练失败'
            }
            return error_output


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练脚本')
    
    # 支持两种参数格式
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--config', type=str, help='配置文件路径')
    group.add_argument('--model_name', type=str, help='模型名称 (与--dataset_id和--train_params一起使用)')
    
    parser.add_argument('--dataset_id', type=int, help='数据集ID')
    parser.add_argument('--train_params', type=str, help='训练参数JSON字符串')

    args = parser.parse_args()

    # 根据参数格式解析配置
    if args.config:
        # 从配置文件读取参数
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            model_name = config.get('model_name', '')
            dataset_id = config.get('dataset_id', 0)
            train_params = config.get('train_params', {})
            
            if not model_name:
                error_output = {
                    'status': 'error',
                    'error_message': '配置文件中缺少model_name',
                    'model_path': '',
                    'onnx_path': '',
                    'metrics': {},
                    'message': '配置文件格式错误'
                }
                print(json.dumps(error_output, ensure_ascii=False))
                return False
                
            if not dataset_id:
                error_output = {
                    'status': 'error',
                    'error_message': '配置文件中缺少dataset_id',
                    'model_path': '',
                    'onnx_path': '',
                    'metrics': {},
                    'message': '配置文件格式错误'
                }
                print(json.dumps(error_output, ensure_ascii=False))
                return False
                
        except FileNotFoundError:
            error_output = {
                'status': 'error',
                'error_message': f'配置文件不存在: {args.config}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '配置文件不存在'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False
        except json.JSONDecodeError as e:
            error_output = {
                'status': 'error',
                'error_message': f'配置文件JSON解析失败: {e}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '配置文件格式错误'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False
        except Exception as e:
            error_output = {
                'status': 'error',
                'error_message': f'读取配置文件失败: {e}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '配置文件读取失败'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False
    else:
        # 从命令行参数读取
        if not dataset_id or not args.train_params:
            error_output = {
                'status': 'error',
                'error_message': '使用--model_name时必须同时提供--dataset_id和--train_params',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '参数不完整'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False
            
        model_name = model_name
        dataset_id = dataset_id
        
        try:
            train_params = json.loads(args.train_params)
        except json.JSONDecodeError as e:
            error_output = {
                'status': 'error',
                'error_message': f'训练参数JSON解析失败: {e}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '参数解析失败'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False

    # 创建训练器并开始训练
    try:
        trainer = CellposeTrainer(
            model_name=args.model_name,
            dataset_id=args.dataset_id,
            train_params=train_params
        )

        result = trainer.train()
        print(json.dumps(result, ensure_ascii=False))

        return result['status'] == 'success'

    except Exception as e:
        print(f"[ERROR] 训练器初始化失败: {e}")
        traceback.print_exc()

        # 输出错误结果到stdout
        error_output = {
            'status': 'error',
            'error_message': str(e),
            'model_path': '',
            'export_path': '',
            'metrics': {},
            'message': '训练器初始化失败'
        }
        print(json.dumps(error_output, ensure_ascii=False))

        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
