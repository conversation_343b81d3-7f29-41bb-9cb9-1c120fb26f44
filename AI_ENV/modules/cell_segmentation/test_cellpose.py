"""
Cellpose细胞分割测试脚本
基于Cellpose的细胞分割模型测试和评估
"""
import argparse
import json
import os
import sys
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
anylabeling_root = project_root / "anylabeling"
sys.path.insert(0, str(anylabeling_root))

from anylabeling.utils.db_manager import DatabaseManager

try:
    from cellpose import models, io, utils
    from cellpose.core import use_gpu
    import numpy as np
    import cv2
    from PIL import Image
    import tifffile
    import matplotlib.pyplot as plt
    from skimage import measure, segmentation
    from scipy import ndimage
except ImportError as e:
    print(f"[ERROR] 缺少必要的依赖包: {e}")
    print("[ERROR] 请安装: pip install cellpose opencv-python pillow tifffile matplotlib scikit-image scipy")
    sys.exit(1)


class CellposeTester:
    """Cellpose细胞分割测试器"""
    
    def __init__(self, model_path: str, test_params: Dict[str, Any], output_path: str = None):
        """
        初始化测试器
        
        Args:
            model_path: 模型文件路径
            test_params: 测试参数
            output_path: 输出路径（可选，默认为模型目录下的test_results）
        """
        self.model_path = model_path
        self.test_params = test_params
        
        # 设置输出目录
        if output_path:
            self.output_path = Path(output_path)
        else:
            model_dir = Path(model_path).parent
            self.output_path = model_dir / "test_results"
        
        self.output_path.mkdir(exist_ok=True)
        
        # 检查GPU可用性
        self.use_gpu = use_gpu()
        
        # 加载模型
        self.model = None
        self._load_model()
        
        # 测试结果
        self.test_results = {}
        self.test_log = []
    
    def _log(self, message: str):
        """记录日志"""
        print(f"[INFO] {message}")
        self.test_log.append(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}")
    
    def _load_model(self):
        """加载Cellpose模型"""
        try:
            self._log(f"加载Cellpose模型: {self.model_path}")
            
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
            
            # 加载自定义训练的模型
            self.model = models.CellposeModel(
                gpu=self.use_gpu,
                pretrained_model=self.model_path
            )
            
            self._log(f"模型加载成功，使用GPU: {self.use_gpu}")
            
        except Exception as e:
            self._log(f"模型加载失败: {e}")
            raise
    
    def _preprocess_image(self, image_path: str) -> np.ndarray:
        """预处理图片"""
        try:
            # 读取图片
            if image_path.lower().endswith(('.tif', '.tiff')):
                image = tifffile.imread(image_path)
            else:
                #image = cv2.imread(image_path)
                image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                if image is not None:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            if image is None:
                raise ValueError(f"无法读取图片: {image_path}")
            
            # 确保图片是RGB格式
            if len(image.shape) == 3 and image.shape[2] == 3:
                # RGB图片
                pass
            elif len(image.shape) == 2:
                # 灰度图片，转换为RGB
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
            else:
                raise ValueError(f"不支持的图片格式: {image.shape}")
            
            return image
            
        except Exception as e:
            self._log(f"图片预处理失败 {image_path}: {e}")
            raise
    
    def _run_inference(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """运行推理"""
        try:
            # 获取推理参数
            diameter = self.test_params.get('diameter', None)  # None表示自动检测
            flow_threshold = self.test_params.get('flow_threshold', 0.4)
            cellprob_threshold = self.test_params.get('cellprob_threshold', 0.0)
            
            # 运行分割
            masks, flows, styles, diams = self.model.eval(
                image,
                diameter=diameter,
                channels=[0, 0],  # 灰度图像
                flow_threshold=flow_threshold,
                cellprob_threshold=cellprob_threshold,
                do_3D=False
            )
            
            return masks, flows[0], diams
            
        except Exception as e:
            self._log(f"推理失败: {e}")
            raise
    
    def _calculate_metrics(self, pred_masks: np.ndarray, gt_masks: np.ndarray) -> Dict[str, float]:
        """计算评估指标"""
        try:
            # 获取实例数量
            pred_instances = len(np.unique(pred_masks)) - 1  # 减去背景
            gt_instances = len(np.unique(gt_masks)) - 1
            
            # 计算IoU指标
            ious = []
            matched_pred = set()
            matched_gt = set()
            
            for gt_id in np.unique(gt_masks):
                if gt_id == 0:  # 跳过背景
                    continue
                
                gt_mask = (gt_masks == gt_id)
                best_iou = 0
                best_pred_id = 0
                
                for pred_id in np.unique(pred_masks):
                    if pred_id == 0 or pred_id in matched_pred:
                        continue
                    
                    pred_mask = (pred_masks == pred_id)
                    
                    # 计算IoU
                    intersection = np.logical_and(gt_mask, pred_mask).sum()
                    union = np.logical_or(gt_mask, pred_mask).sum()
                    
                    if union > 0:
                        iou = intersection / union
                        if iou > best_iou:
                            best_iou = iou
                            best_pred_id = pred_id
                
                if best_iou > 0.5:  # IoU阈值
                    ious.append(best_iou)
                    matched_pred.add(best_pred_id)
                    matched_gt.add(gt_id)
            
            # 计算指标
            mean_iou = np.mean(ious) if ious else 0.0
            precision = len(matched_pred) / pred_instances if pred_instances > 0 else 0.0
            recall = len(matched_gt) / gt_instances if gt_instances > 0 else 0.0
            f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
            
            return {
                'mean_iou': mean_iou,
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score,
                'pred_instances': pred_instances,
                'gt_instances': gt_instances,
                'matched_instances': len(matched_pred)
            }
            
        except Exception as e:
            self._log(f"指标计算失败: {e}")
            return {
                'mean_iou': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'pred_instances': 0,
                'gt_instances': 0,
                'matched_instances': 0
            }
    
    def _visualize_results(self, image: np.ndarray, pred_masks: np.ndarray, 
                          gt_masks: np.ndarray, save_path: str):
        """可视化结果"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 12))
            
            # 原始图片
            axes[0, 0].imshow(image)
            axes[0, 0].set_title('Original Image')
            axes[0, 0].axis('off')
            
            # 预测掩码
            axes[0, 1].imshow(pred_masks, cmap='tab20')
            axes[0, 1].set_title(f'Predicted Masks ({len(np.unique(pred_masks))-1} cells)')
            axes[0, 1].axis('off')
            
            # 真实掩码
            axes[1, 0].imshow(gt_masks, cmap='tab20')
            axes[1, 0].set_title(f'Ground Truth Masks ({len(np.unique(gt_masks))-1} cells)')
            axes[1, 0].axis('off')
            
            # 叠加显示
            overlay = image.copy()
            pred_contours = segmentation.find_boundaries(pred_masks, mode='thick')
            gt_contours = segmentation.find_boundaries(gt_masks, mode='thick')
            
            overlay[pred_contours] = [255, 0, 0]  # 红色：预测
            overlay[gt_contours] = [0, 255, 0]    # 绿色：真实
            
            axes[1, 1].imshow(overlay)
            axes[1, 1].set_title('Overlay (Red: Pred, Green: GT)')
            axes[1, 1].axis('off')
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            self._log(f"可视化结果已保存: {save_path}")
            
        except Exception as e:
            self._log(f"可视化失败: {e}")
    
    def test_on_images(self, test_images: List[str], gt_masks: List[str] = None) -> Dict[str, Any]:
        """在图片列表上进行测试"""
        try:
            self._log(f"开始测试，共 {len(test_images)} 张图片")
            
            all_metrics = []
            results = []
            
            for i, image_path in enumerate(test_images):
                self._log(f"处理图片 {i+1}/{len(test_images)}: {os.path.basename(image_path)}")
                
                try:
                    # 预处理图片
                    image = self._preprocess_image(image_path)
                    
                    # 运行推理
                    pred_masks, flows, diams = self._run_inference(image)
                    
                    # 保存预测结果
                    image_name = os.path.splitext(os.path.basename(image_path))[0]
                    pred_save_path = self.output_path / f"{image_name}_pred_masks.tif"
                    tifffile.imwrite(str(pred_save_path), pred_masks.astype(np.uint16))
                    
                    result = {
                        'image_path': image_path,
                        'pred_masks_path': str(pred_save_path),
                        'pred_instances': len(np.unique(pred_masks)) - 1,
                        'diameter': diams
                    }
                    
                    # 如果有真实标签，计算指标
                    if gt_masks and i < len(gt_masks):
                        gt_mask = tifffile.imread(gt_masks[i])
                        metrics = self._calculate_metrics(pred_masks, gt_mask)
                        result.update(metrics)
                        all_metrics.append(metrics)
                        
                        # 可视化结果
                        vis_save_path = self.output_path / f"{image_name}_visualization.png"
                        self._visualize_results(image, pred_masks, gt_mask, str(vis_save_path))
                    
                    results.append(result)
                    
                except Exception as e:
                    self._log(f"处理图片失败 {image_path}: {e}")
                    continue
            
            # 计算平均指标
            avg_metrics = {}
            if all_metrics:
                for key in all_metrics[0].keys():
                    avg_metrics[f'avg_{key}'] = np.mean([m[key] for m in all_metrics])
            
            test_summary = {
                'total_images': len(test_images),
                'processed_images': len(results),
                'failed_images': len(test_images) - len(results),
                'average_metrics': avg_metrics,
                'detailed_results': results
            }
            
            self._log("测试完成")
            return test_summary
            
        except Exception as e:
            self._log(f"测试失败: {e}")
            raise
    
    def save_test_results(self, test_summary: Dict[str, Any]):
        """保存测试结果"""
        try:
            # 保存测试结果
            results_file = self.output_path / 'test_results.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(test_summary, f, ensure_ascii=False, indent=2)
            
            # 保存测试日志
            log_file = self.output_path / 'test_log.txt'
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.test_log))
            
            self._log(f"测试结果已保存: {results_file}")
            self._log(f"测试日志已保存: {log_file}")
            
        except Exception as e:
            self._log(f"保存测试结果失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Cellpose细胞分割测试脚本')
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--test_images', type=str, required=True, help='测试图片目录或文件列表')
    parser.add_argument('--output_path', type=str, help='结果输出路径（可选）')
    parser.add_argument('--gt_masks', type=str, help='真实掩码目录或文件列表（可选）')
    parser.add_argument('--test_params', type=str, default='{}', help='测试参数JSON字符串')
    
    args = parser.parse_args()
    
    # 解析测试参数
    try:
        test_params = json.loads(args.test_params)
    except json.JSONDecodeError as e:
        print(f"[ERROR] 测试参数JSON解析失败: {e}")
        return False
    
    # 获取测试图片列表
    test_images = []
    if os.path.isdir(args.test_images):
        # 目录
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.tif', '*.tiff']:
            test_images.extend(Path(args.test_images).glob(ext))
        test_images = [str(p) for p in test_images]
    else:
        # 文件列表
        with open(args.test_images, 'r') as f:
            test_images = [line.strip() for line in f if line.strip()]
    
    # 获取真实掩码列表
    gt_masks = None
    if args.gt_masks:
        if os.path.isdir(args.gt_masks):
            # 目录
            gt_masks = []
            for img_path in test_images:
                img_name = os.path.splitext(os.path.basename(img_path))[0]
                mask_path = os.path.join(args.gt_masks, f"{img_name}_masks.tif")
                if os.path.exists(mask_path):
                    gt_masks.append(mask_path)
                else:
                    gt_masks.append(None)
        else:
            # 文件列表
            with open(args.gt_masks, 'r') as f:
                gt_masks = [line.strip() for line in f if line.strip()]
    
    # 创建测试器并开始测试
    try:
        tester = CellposeTester(
            model_path=args.model_path,
            test_params=test_params,
            output_path=args.output_path
        )
        
        test_summary = tester.test_on_images(test_images, gt_masks)
        tester.save_test_results(test_summary)
        
        print(json.dumps(test_summary, ensure_ascii=False, indent=2))
        return True
        
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
