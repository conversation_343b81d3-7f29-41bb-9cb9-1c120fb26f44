"""
YOLO目标检测训练脚本
训练YOLOv8模型并自动导出为ONNX格式
"""
import argparse
import os
import sys
import json
import traceback
import shutil
import random
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入数据库管理器
try:
    from anylabeling.utils.db_manager import DatabaseManager
except ImportError:
    # 如果无法导入，尝试从anylabeling.utils导入
    from anylabeling.utils.db_manager import DatabaseManager

try:
    from ultralytics import YOLO
    import torch
    import yaml
    import cv2
    import numpy as np
except ImportError as e:
    print(f"[ERROR] 缺少必要的依赖包: {e}")
    print("[ERROR] 请安装: pip install ultralytics torch pyyaml opencv-python numpy")
    sys.exit(1)


class YOLOObjectDetectionTrainer:
    """YOLO目标检测训练器"""

    def __init__(self, model_name: str, dataset_id: int, train_params: Dict[str, Any], config: Dict[str, Any] = None):
        """
        初始化训练器

        Args:
            model_name: 模型名称
            dataset_id: 数据集ID
            train_params: 训练参数
            config: 完整配置信息（可选）
        """
        self.model_name = model_name
        self.dataset_id = dataset_id
        self.train_params = train_params
        self.config = config or {}

        # 初始化数据库管理器
        self.db_manager = DatabaseManager()

        # 获取数据集信息
        self.dataset_info = self.db_manager.get_dataset(dataset_id)
        if not self.dataset_info:
            # 尝试通过路径查找数据集
            dataset_path = self.config.get('dataset_path')
            if dataset_path:
                self.dataset_info = self.db_manager.get_dataset_by_path(dataset_path)
            
            if not self.dataset_info:
                raise ValueError(f"无法获取数据集信息: {dataset_id}, 路径: {dataset_path}")

        # 设置输出目录
        self.output_base_dir = Path("D:/anylabeling2/AI_ENV/output/object_detection")
        self.model_output_path = self.output_base_dir / model_name
        self.model_output_path.mkdir(parents=True, exist_ok=True)

        # 创建YOLO数据集目录
        self.yolo_dataset_path = self.model_output_path / "dataset"
        self.yolo_dataset_path.mkdir(exist_ok=True)

        # 初始化模型
        self.model = None

        # 训练结果
        self.training_results = {}
        self.training_log = []
    
    def _log(self, message: str):
        """记录日志"""
        print(f"[INFO] {message}")
        self.training_log.append(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}")

    def _get_dataset_annotations(self) -> List[Dict[str, Any]]:
        """获取数据集的所有标注信息"""
        try:
            annotations = self.db_manager.get_annotations_for_dataset(self.dataset_id)
            self._log(f"获取到 {len(annotations)} 个标注记录")
            return annotations
        except Exception as e:
            raise Exception(f"获取数据集标注失败: {e}")

    def _extract_labels_from_annotations(self, annotations: List[Dict[str, Any]]) -> Dict[str, int]:
        """从标注文件中提取所有标签"""
        labels = set()
        dataset_dir = self.dataset_info['output_dir']
        labels_dir = os.path.join(dataset_dir, 'labels')

        for annotation in annotations:
            image_path = annotation['image_path']
            label_file = os.path.splitext(os.path.basename(image_path))[0] + ".json"
            label_path = os.path.join(labels_dir, label_file)

            if os.path.exists(label_path):
                try:
                    with open(label_path, 'r', encoding='utf-8') as f:
                        label_data = json.load(f)

                    for shape in label_data.get('shapes', []):
                        labels.add(shape['label'])
                except Exception as e:
                    self._log(f"读取标注文件失败 {label_path}: {e}")

        # 创建标签到索引的映射
        label_to_idx = {label: idx for idx, label in enumerate(sorted(labels))}
        self._log(f"提取到标签: {list(label_to_idx.keys())}")
        return label_to_idx
    
    def _convert_labelme_to_yolo(self, label_data: Dict, image_width: int, image_height: int,
                                label_to_idx: Dict[str, int]) -> List[str]:
        """将LabelMe格式的标注转换为YOLO格式，支持rectangle、polygon、circle"""
        yolo_lines = []

        for shape in label_data.get('shapes', []):
            label = shape['label']
            if label not in label_to_idx:
                continue

            class_id = label_to_idx[label]
            points = shape['points']
            shape_type = shape['shape_type']

            # 根据不同的形状类型计算边界框
            if shape_type == 'rectangle':
                # 矩形：直接使用两个对角点
                x1, y1 = points[0]
                x2, y2 = points[1]
                x_min, x_max = min(x1, x2), max(x1, x2)
                y_min, y_max = min(y1, y2), max(y1, y2)
                
            elif shape_type == 'polygon':
                # 多边形：计算包围所有点的最小边界框
                if len(points) < 3:
                    self._log(f"警告: 多边形点数不足，跳过标签 {label}")
                    continue
                    
                x_coords = [point[0] for point in points]
                y_coords = [point[1] for point in points]
                x_min, x_max = min(x_coords), max(x_coords)
                y_min, y_max = min(y_coords), max(y_coords)
                
            elif shape_type == 'circle':
                # 圆形：根据圆心和半径计算边界框
                if len(points) < 2:
                    self._log(f"警告: 圆形点数不足，跳过标签 {label}")
                    continue
                    
                center_x, center_y = points[0]
                edge_x, edge_y = points[1]
                
                # 计算半径
                radius = ((edge_x - center_x) ** 2 + (edge_y - center_y) ** 2) ** 0.5
                
                # 计算边界框
                x_min = center_x - radius
                x_max = center_x + radius
                y_min = center_y - radius
                y_max = center_y + radius
                
            else:
                # 不支持的形状类型，跳过
                self._log(f"警告: 不支持的形状类型 {shape_type}，跳过标签 {label}")
                continue

            # 确保边界框在图像范围内
            x_min = max(0, min(x_min, image_width))
            x_max = max(0, min(x_max, image_width))
            y_min = max(0, min(y_min, image_height))
            y_max = max(0, min(y_max, image_height))

            # 检查边界框是否有效
            if x_max <= x_min or y_max <= y_min:
                self._log(f"警告: 无效的边界框，跳过标签 {label}")
                continue

            # 转换为YOLO格式 (中心点坐标和宽高，归一化)
            center_x = (x_min + x_max) / 2.0 / image_width
            center_y = (y_min + y_max) / 2.0 / image_height
            width = (x_max - x_min) / image_width
            height = (y_max - y_min) / image_height

            yolo_lines.append(f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}")

        return yolo_lines
    
    def _prepare_yolo_dataset(self, annotations: List[Dict[str, Any]],
                             label_to_idx: Dict[str, int]) -> Tuple[List[str], List[str]]:
        """准备YOLO格式的数据集"""
        dataset_dir = self.dataset_info['output_dir']
        labels_dir = os.path.join(dataset_dir, 'labels')

        valid_samples = []
        failed_samples = []

        for annotation in annotations:
            image_path = annotation['image_path']
            if not os.path.exists(image_path):
                failed_samples.append(f"图片不存在: {image_path}")
                continue

            # 读取图片获取尺寸
            try:
                #image = cv2.imread(image_path)
                image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                if image is None:
                    failed_samples.append(f"无法读取图片: {image_path}")
                    continue

                image_height, image_width = image.shape[:2]
            except Exception as e:
                failed_samples.append(f"读取图片失败 {image_path}: {e}")
                continue

            # 读取标注文件
            label_file = os.path.splitext(os.path.basename(image_path))[0] + ".json"
            label_path = os.path.join(labels_dir, label_file)

            if not os.path.exists(label_path):
                failed_samples.append(f"标注文件不存在: {label_path}")
                continue

            try:
                with open(label_path, 'r', encoding='utf-8') as f:
                    label_data = json.load(f)

                # 转换为YOLO格式
                yolo_lines = self._convert_labelme_to_yolo(label_data, image_width, image_height, label_to_idx)

                if yolo_lines:  # 只有有标注的图片才加入训练
                    valid_samples.append({
                        'image_path': image_path,
                        'yolo_labels': yolo_lines
                    })
                else:
                    failed_samples.append(f"无有效标注: {image_path}")

            except Exception as e:
                failed_samples.append(f"处理标注失败 {label_path}: {e}")

        self._log(f"有效样本: {len(valid_samples)}, 失败样本: {len(failed_samples)}")
        if failed_samples:
            self._log(f"失败样本详情: {failed_samples[:5]}...")  # 只显示前5个

        return valid_samples, failed_samples
    
    def _split_dataset(self, valid_samples: List[Dict], train_ratio: float = 0.8) -> Tuple[List[Dict], List[Dict]]:
        """分割数据集为训练集和验证集"""
        random.shuffle(valid_samples)

        train_size = int(len(valid_samples) * train_ratio)
        train_samples = valid_samples[:train_size]
        val_samples = valid_samples[train_size:]

        self._log(f"数据集分割: 训练集 {len(train_samples)}, 验证集 {len(val_samples)}")
        return train_samples, val_samples

    def _create_yolo_dataset_structure(self, train_samples: List[Dict], val_samples: List[Dict],
                                     label_to_idx: Dict[str, int]) -> str:
        """创建YOLO数据集目录结构"""
        # 创建目录结构
        for split in ['train', 'val']:
            (self.yolo_dataset_path / 'images' / split).mkdir(parents=True, exist_ok=True)
            (self.yolo_dataset_path / 'labels' / split).mkdir(parents=True, exist_ok=True)

        # 处理训练集
        self._copy_samples_to_yolo_format(train_samples, 'train')

        # 处理验证集
        self._copy_samples_to_yolo_format(val_samples, 'val')

        # 创建数据集配置文件
        dataset_config = {
            'path': str(self.yolo_dataset_path),
            'train': 'images/train',
            'val': 'images/val',
            'names': {idx: label for label, idx in label_to_idx.items()}
        }

        config_path = self.yolo_dataset_path / 'dataset.yaml'
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)

        self._log(f"YOLO数据集配置文件已创建: {config_path}")
        return str(config_path)

    def _copy_samples_to_yolo_format(self, samples: List[Dict], split: str):
        """将样本复制到YOLO格式目录"""
        for sample in samples:
            image_path = sample['image_path']
            yolo_labels = sample['yolo_labels']

            # 复制图片
            image_name = os.path.basename(image_path)
            dst_image_path = self.yolo_dataset_path / 'images' / split / image_name
            shutil.copy2(image_path, dst_image_path)

            # 创建标签文件
            label_name = os.path.splitext(image_name)[0] + '.txt'
            dst_label_path = self.yolo_dataset_path / 'labels' / split / label_name

            with open(dst_label_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_labels))

    def _train_model(self, dataset_config_path: str) -> Dict[str, Any]:
        """训练模型"""
        try:
            self._log("开始模型训练...")

            # 初始化YOLO模型
            model_size = self.train_params.get('model_size', 'yolov8s.pt')
            self._log(f"初始化YOLO模型: {model_size}")
            self.model = YOLO(model_size)

            # 检查CUDA可用性
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
            self._log(f"使用设备: {device}")

            # 准备训练参数
            train_params = {
                'data': dataset_config_path,
                'epochs': self.train_params.get('epochs', 100),
                'imgsz': self.train_params.get('imgsz', 640),
                'batch': self.train_params.get('batch', 16),
                'lr0': self.train_params.get('lr0', 0.01),
                'device': device,
                'project': str(self.model_output_path),
                'name': 'train',
                'exist_ok': True,
                'verbose': True,
                'save': True,
                'plots': True,
                'val': True
            }

            self._log(f"训练参数: {json.dumps(train_params, ensure_ascii=False, indent=2)}")

            # 开始训练
            results = self.model.train(**train_params)

            # 提取训练指标
            metrics = {}
            if hasattr(results, 'results_dict'):
                metrics = {
                    'train_loss': float(results.results_dict.get('train/box_loss', 0.0)),
                    'val_loss': float(results.results_dict.get('val/box_loss', 0.0)),
                    'mAP50': float(results.results_dict.get('metrics/mAP50(B)', 0.0)),
                    'mAP50_95': float(results.results_dict.get('metrics/mAP50-95(B)', 0.0)),
                    'precision': float(results.results_dict.get('metrics/precision(B)', 0.0)),
                    'recall': float(results.results_dict.get('metrics/recall(B)', 0.0))
                }

            self._log("模型训练完成")
            self._log(f"训练指标: {json.dumps(metrics, ensure_ascii=False, indent=2)}")

            return metrics

        except Exception as e:
            self._log(f"模型训练失败: {e}")
            raise
    
    def _export_to_onnx(self) -> str:
        """导出模型为ONNX格式"""
        try:
            self._log("开始导出ONNX模型...")

            # 获取最佳模型路径
            best_model_path = self.model_output_path / 'train' / 'weights' / 'best.pt'
            if not best_model_path.exists():
                # 尝试last.pt
                best_model_path = self.model_output_path / 'train' / 'weights' / 'last.pt'
                if not best_model_path.exists():
                    raise FileNotFoundError("找不到训练好的模型文件")

            self._log(f"加载模型: {best_model_path}")

            # 加载最佳模型
            best_model = YOLO(str(best_model_path))

            # 导出参数
            export_params = {
                'format': 'onnx',
                'dynamic': True,  # 支持动态batch size
                'simplify': True,
                'opset': 11,
                'half': False
            }

            self._log(f"ONNX导出参数: {json.dumps(export_params, ensure_ascii=False, indent=2)}")

            # 执行导出
            onnx_path = best_model.export(**export_params)

            # 移动ONNX文件到模型输出目录
            onnx_filename = f"{self.model_name}.onnx"
            final_onnx_path = self.model_output_path / onnx_filename

            if os.path.exists(onnx_path) and str(onnx_path) != str(final_onnx_path):
                shutil.move(str(onnx_path), str(final_onnx_path))
                onnx_path = str(final_onnx_path)

            self._log(f"ONNX模型已导出: {onnx_path}")
            return str(onnx_path)

        except Exception as e:
            self._log(f"ONNX导出失败: {e}")
            raise
    
    def _save_training_results(self, metrics: Dict[str, Any], onnx_path: str) -> Dict[str, Any]:
        """保存训练结果"""
        try:
            # 获取模型文件路径
            model_path = self.model_output_path / 'train' / 'weights' / 'best.pt'
            if not model_path.exists():
                model_path = self.model_output_path / 'train' / 'weights' / 'last.pt'

            # 保存训练配置
            training_config = {
                'model_name': self.model_name,
                'dataset_id': self.dataset_id,
                'training_params': self.train_params,
                'training_time': datetime.now().isoformat(),
                'model_architecture': 'YOLOv8'
            }

            config_file = self.model_output_path / 'training_config.json'
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(training_config, f, ensure_ascii=False, indent=2)

            # 保存训练指标
            metrics_file = self.model_output_path / 'training_metrics.json'
            with open(metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, ensure_ascii=False, indent=2)

            # 保存训练日志
            log_file = self.model_output_path / 'training_log.txt'
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.training_log))

            self._log("训练结果已保存")
            self._log(f"模型文件: {model_path}")
            self._log(f"ONNX文件: {onnx_path}")
            self._log(f"配置文件: {config_file}")
            self._log(f"指标文件: {metrics_file}")
            self._log(f"日志文件: {log_file}")

            return {
                'model_path': str(model_path),
                'onnx_path': onnx_path,
                'config_path': str(config_file),
                'metrics_path': str(metrics_file),
                'log_path': str(log_file),
                'metrics': metrics
            }

        except Exception as e:
            self._log(f"保存训练结果失败: {e}")
            raise
    
    def train(self) -> Dict[str, Any]:
        """执行完整的训练流程"""
        try:
            self._log(f"开始YOLO目标检测模型训练")
            self._log(f"模型名称: {self.model_name}")
            self._log(f"数据集ID: {self.dataset_id}")
            self._log(f"输出目录: {self.model_output_path}")

            # 1. 获取数据集标注
            annotations = self._get_dataset_annotations()
            if not annotations:
                raise Exception("数据集中没有标注数据")

            # 2. 提取标签
            label_to_idx = self._extract_labels_from_annotations(annotations)
            if not label_to_idx:
                raise Exception("数据集中没有有效标签")

            # 3. 准备YOLO格式数据集
            valid_samples, failed_samples = self._prepare_yolo_dataset(annotations, label_to_idx)
            if not valid_samples:
                raise Exception("没有有效的训练样本")

            # 4. 分割数据集
            train_ratio = self.train_params.get('train_ratio', 0.8)
            train_samples, val_samples = self._split_dataset(valid_samples, train_ratio)

            # 5. 创建YOLO数据集结构
            dataset_config_path = self._create_yolo_dataset_structure(train_samples, val_samples, label_to_idx)

            # 6. 训练模型
            metrics = self._train_model(dataset_config_path)

            # 7. 导出ONNX
            onnx_path = self._export_to_onnx()

            # 8. 保存结果
            results = self._save_training_results(metrics, onnx_path)

            # 输出最终结果
            final_output = {
                'status': 'success',
                'model_path': results['model_path'],
                'onnx_path': results['onnx_path'],
                'metrics': results['metrics'],
                'train_samples': len(train_samples),
                'val_samples': len(val_samples),
                'failed_samples': len(failed_samples),
                'message': '训练和ONNX导出完成'
            }

            self._log("训练流程完成！")
            return final_output

        except Exception as e:
            self._log(f"训练流程失败: {e}")

            # 输出错误结果
            error_output = {
                'status': 'error',
                'error_message': str(e),
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '训练失败'
            }
            return error_output


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='YOLO目标检测训练脚本')

    # 支持两种参数格式
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--config', type=str, help='配置文件路径')
    group.add_argument('--model_name', type=str, help='模型名称 (与--dataset_id和--train_params一起使用)')

    parser.add_argument('--dataset_id', type=int, help='数据集ID')
    parser.add_argument('--train_params', type=str, help='训练参数JSON字符串')

    args = parser.parse_args()

    # 根据参数格式解析配置
    if args.config:
        # 从配置文件读取参数
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                config = json.load(f)

            model_name = config.get('model_name', '')
            dataset_id = config.get('dataset_id', 0)
            train_params = config.get('train_params', {})

            if not model_name:
                error_output = {
                    'status': 'error',
                    'error_message': '配置文件中缺少model_name',
                    'model_path': '',
                    'onnx_path': '',
                    'metrics': {},
                    'message': '配置文件格式错误'
                }
                print(json.dumps(error_output, ensure_ascii=False))
                return False

            if not dataset_id:
                error_output = {
                    'status': 'error',
                    'error_message': '配置文件中缺少dataset_id',
                    'model_path': '',
                    'onnx_path': '',
                    'metrics': {},
                    'message': '配置文件格式错误'
                }
                print(json.dumps(error_output, ensure_ascii=False))
                return False

        except FileNotFoundError:
            error_output = {
                'status': 'error',
                'error_message': f'配置文件不存在: {args.config}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '配置文件不存在'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False
        except json.JSONDecodeError as e:
            error_output = {
                'status': 'error',
                'error_message': f'配置文件JSON解析失败: {e}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '配置文件格式错误'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False
        except Exception as e:
            error_output = {
                'status': 'error',
                'error_message': f'读取配置文件失败: {e}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '配置文件读取失败'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False
    else:
        # 从命令行参数读取
        if not args.dataset_id or not args.train_params:
            error_output = {
                'status': 'error',
                'error_message': '使用--model_name时必须同时提供--dataset_id和--train_params',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '参数不完整'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False

        model_name = args.model_name
        dataset_id = args.dataset_id

        try:
            train_params = json.loads(args.train_params)
        except json.JSONDecodeError as e:
            error_output = {
                'status': 'error',
                'error_message': f'训练参数JSON解析失败: {e}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '参数解析失败'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False

    # 创建训练器并开始训练
    try:
        trainer = YOLOObjectDetectionTrainer(
            model_name=model_name,
            dataset_id=dataset_id,
            train_params=train_params,
            config=config if args.config else {}
        )

        result = trainer.train()
        print(json.dumps(result, ensure_ascii=False))

        return result['status'] == 'success'

    except Exception as e:
        print(f"[ERROR] 训练器初始化失败: {e}")
        traceback.print_exc()

        # 输出错误结果到stdout
        error_output = {
            'status': 'error',
            'error_message': str(e),
            'model_path': '',
            'onnx_path': '',
            'metrics': {},
            'message': '训练器初始化失败'
        }
        print(json.dumps(error_output, ensure_ascii=False))

        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
