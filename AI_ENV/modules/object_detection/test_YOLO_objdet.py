"""
YOLO目标检测测试脚本
基于YOLOv8的目标检测模型测试和评估
"""
import argparse
import json
import os
import sys
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
anylabeling_root = project_root / "anylabeling"
sys.path.insert(0, str(anylabeling_root))

try:
    from ultralytics import YOLO
    import torch
    import cv2
    import numpy as np
    from PIL import Image, ImageDraw
    import matplotlib.pyplot as plt
except ImportError as e:
    print(f"[ERROR] 缺少必要的依赖包: {e}")
    print("[ERROR] 请安装: pip install ultralytics torch opencv-python pillow matplotlib")
    sys.exit(1)


class YOLOObjectDetectionTester:
    """YOLO目标检测测试器"""
    
    def __init__(self, model_path: str, test_params: Dict[str, Any], output_path: str = None):
        """
        初始化测试器
        
        Args:
            model_path: 模型文件路径
            test_params: 测试参数
            output_path: 输出路径（可选，默认为模型目录下的test_results）
        """
        self.model_path = model_path
        self.test_params = test_params
        
        # 初始化测试结果和日志（必须在_load_model之前）
        self.test_results = {}
        self.test_log = []
        
        # 设置输出目录
        if output_path:
            self.output_path = Path(output_path)
        else:
            model_dir = Path(model_path).parent
            self.output_path = model_dir / "test_results"
        
        self.output_path.mkdir(exist_ok=True)
        
        # 加载模型
        self.model = None
        self._load_model()
    
    def _log(self, message: str):
        """记录日志"""
        print(f"[INFO] {message}")
        self.test_log.append(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}")
    
    def _load_model(self):
        """加载YOLO模型"""
        try:
            self._log(f"加载YOLO目标检测模型: {self.model_path}")
            
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
            
            self.model = YOLO(self.model_path, task='detect')
            
            # 检查CUDA可用性
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
            self._log(f"模型加载成功，使用设备: {device}")
            
        except Exception as e:
            self._log(f"模型加载失败: {e}")
            raise
    
    def _run_inference(self, image_path: str) -> Dict[str, Any]:
        """运行推理"""
        try:
            # 获取推理参数
            conf_threshold = self.test_params.get('confidence_threshold', self.test_params.get('conf_threshold', 0.25))
            iou_threshold = self.test_params.get('iou_threshold', 0.7)
            imgsz = self.test_params.get('imgsz', 640)
            
            # 运行推理
            results = self.model(
                image_path,
                conf=conf_threshold,
                iou=iou_threshold,
                imgsz=imgsz,
                save=False,
                verbose=False
            )
            
            result = results[0]
            
            # 提取结果
            boxes = result.boxes.xyxy.cpu().numpy() if result.boxes is not None else np.array([])
            scores = result.boxes.conf.cpu().numpy() if result.boxes is not None else np.array([])
            classes = result.boxes.cls.cpu().numpy() if result.boxes is not None else np.array([])
            
            return {
                'boxes': boxes,
                'scores': scores,
                'classes': classes,
                'image_shape': result.orig_shape
            }
            
        except Exception as e:
            self._log(f"推理失败: {e}")
            raise
    
    def _visualize_results(self, image_path: str, pred_result: Dict, save_path: str, class_names: List[str] = None):
        """可视化结果"""
        try:
            # 读取图片
            #image = cv2.imread(image_path)
            image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 创建图形
            fig, axes = plt.subplots(1, 2, figsize=(15, 7))
            
            # 原始图片
            axes[0].imshow(image)
            axes[0].set_title('Original Image')
            axes[0].axis('off')
            
            # 预测结果
            result_image = image.copy()
            
            boxes = pred_result['boxes']
            scores = pred_result['scores']
            classes = pred_result['classes']
            
            # 绘制边界框
            for i in range(len(boxes)):
                box = boxes[i]
                score = scores[i]
                cls = int(classes[i])
                
                x1, y1, x2, y2 = box.astype(int)
                
                # 绘制边界框
                cv2.rectangle(result_image, (x1, y1), (x2, y2), (255, 0, 0), 2)
                
                # 绘制标签
                if class_names and cls < len(class_names):
                    label = f'{class_names[cls]}: {score:.2f}'
                else:
                    label = f'Class {cls}: {score:.2f}'
                
                cv2.putText(result_image, label, (x1, y1-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            
            axes[1].imshow(result_image)
            axes[1].set_title(f'Predictions ({len(boxes)} objects)')
            axes[1].axis('off')
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            self._log(f"可视化结果已保存: {save_path}")
            
        except Exception as e:
            self._log(f"可视化失败: {e}")
    
    def test_on_images(self, test_images: List[str], class_names: List[str] = None) -> Dict[str, Any]:
        """在图片列表上进行测试"""
        try:
            self._log(f"开始测试，共 {len(test_images)} 张图片")
            
            results = []
            
            for i, image_path in enumerate(test_images):
                self._log(f"处理图片 {i+1}/{len(test_images)}: {os.path.basename(image_path)}")
                
                try:
                    # 运行推理
                    pred_result = self._run_inference(image_path)
                    
                    # 保存预测结果
                    image_name = os.path.splitext(os.path.basename(image_path))[0]
                    
                    result = {
                        'image_path': image_path,
                        'predictions': {
                            'boxes': pred_result['boxes'].tolist(),
                            'scores': pred_result['scores'].tolist(),
                            'classes': pred_result['classes'].tolist(),
                            'num_objects': len(pred_result['boxes'])
                        }
                    }
                    
                    results.append(result)
                    
                    # 可视化结果
                    vis_save_path = self.output_path / f"{image_name}_visualization.png"
                    self._visualize_results(image_path, pred_result, str(vis_save_path), class_names)
                    
                except Exception as e:
                    self._log(f"处理图片失败 {image_path}: {e}")
                    continue
            
            # 计算统计信息
            total_objects = sum(result['predictions']['num_objects'] for result in results)
            avg_objects = total_objects / len(results) if results else 0
            
            test_summary = {
                'total_images': len(test_images),
                'processed_images': len(results),
                'failed_images': len(test_images) - len(results),
                'total_objects_detected': total_objects,
                'average_objects_per_image': avg_objects,
                'detailed_results': results
            }
            
            self._log("测试完成")
            return test_summary
            
        except Exception as e:
            self._log(f"测试失败: {e}")
            raise
    
    def save_test_results(self, test_summary: Dict[str, Any]):
        """保存测试结果"""
        try:
            # 保存测试结果
            results_file = self.output_path / 'test_results.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(test_summary, f, ensure_ascii=False, indent=2)
            
            # 保存测试日志
            log_file = self.output_path / 'test_log.txt'
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.test_log))
            
            self._log(f"测试结果已保存: {results_file}")
            self._log(f"测试日志已保存: {log_file}")
            
        except Exception as e:
            self._log(f"保存测试结果失败: {e}")


def main():
    """主函数"""
    print("[DEBUG] 测试脚本启动")
    
    parser = argparse.ArgumentParser(description='YOLO目标检测测试脚本')
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--test_images', type=str, required=True, help='测试图片目录或文件列表')
    parser.add_argument('--output_path', type=str, help='结果输出路径（可选）')
    parser.add_argument('--class_names', type=str, help='类别名称文件（可选）')
    parser.add_argument('--test_params', type=str, default='{}', help='测试参数JSON字符串')
    
    args = parser.parse_args()
    print(f"[DEBUG] 参数解析完成: model_path={args.model_path}, test_images={args.test_images}")
    
    # 解析测试参数
    try:
        test_params = json.loads(args.test_params)
        print(f"[DEBUG] 测试参数解析成功: {test_params}")
    except json.JSONDecodeError as e:
        print(f"[ERROR] 测试参数JSON解析失败: {e}")
        return False
    
    # 获取测试图片列表
    test_images = []
    if os.path.isdir(args.test_images):
        # 目录
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.tif', '*.tiff']:
            test_images.extend(Path(args.test_images).glob(ext))
        test_images = [str(p) for p in test_images]
    else:
        # 文件列表
        with open(args.test_images, 'r') as f:
            test_images = [line.strip() for line in f if line.strip()]
    
    # 获取类别名称
    class_names = None
    if args.class_names and os.path.exists(args.class_names):
        with open(args.class_names, 'r') as f:
            class_names = [line.strip() for line in f if line.strip()]
    
    # 创建测试器并开始测试
    try:
        tester = YOLOObjectDetectionTester(
            model_path=args.model_path,
            test_params=test_params,
            output_path=args.output_path
        )
        
        test_summary = tester.test_on_images(test_images, class_names)
        tester.save_test_results(test_summary)
        
        print(json.dumps(test_summary, ensure_ascii=False, indent=2))
        return True
        
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
