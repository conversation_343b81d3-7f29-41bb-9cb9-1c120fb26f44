"""
语义分割训练脚本
基于PaddleSeg的语义分割模型训练
"""
import argparse
import json
import os
import sys
import traceback
import shutil
import random
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 直接导入utils.db_manager
from anylabeling.utils.db_manager import DatabaseManager

try:
    import paddle
    import paddleseg
    from paddleseg import utils
    from paddleseg.cvlibs import Config
    from paddleseg.cvlibs.builder import SegBuilder
    from paddleseg.core import train
    from paddleseg.core import evaluate
    from paddleseg.datasets import Dataset as PaddleDataset
    from paddleseg.models import *
    from paddleseg.transforms import *
    import paddle2onnx
    import cv2
    import numpy as np
    from PIL import Image, ImageDraw
    import yaml
    from loguru import logger as log
except ImportError as e:
    print(f"[ERROR] 缺少必要的依赖包: {e}")
    print("[ERROR] 请安装: pip install paddlepaddle paddleseg paddle2onnx opencv-python pillow pyyaml loguru")
    sys.exit(1)


class InterceptHandler(logging.Handler):
    def emit(self, record):
        logger_opt = log.opt(depth=6, exception=record.exc_info)
        logger_opt.log(record.levelno, record.getMessage())


def get_paddle_logger():
    logger_name_list = ['paddleseg']
    for logger_name in logger_name_list:
        logging.getLogger(logger_name).setLevel(10)
        logging.getLogger(logger_name).handlers = []
        if '.' not in logger_name:
            logging.getLogger(logger_name).addHandler(InterceptHandler())


class PaddleSegDataset(PaddleDataset):
    """PaddleSeg数据集类"""

    def __init__(self, dataset_root: str, transforms=None, mode='train'):
        super(PaddleSegDataset, self).__init__(
            dataset_root=dataset_root,
            transforms=transforms,
            mode=mode
        )

        # 读取数据列表
        if mode == 'train':
            file_list = os.path.join(dataset_root, 'train_list.txt')
        elif mode == 'val':
            file_list = os.path.join(dataset_root, 'val_list.txt')
        else:
            file_list = os.path.join(dataset_root, 'test_list.txt')

        self.file_list = []
        if os.path.exists(file_list):
            with open(file_list, 'r') as f:
                for line in f:
                    items = line.strip().split()
                    if len(items) >= 2:
                        self.file_list.append({
                            'img': os.path.join(dataset_root, items[0]),
                            'label': os.path.join(dataset_root, items[1])
                        })

    def __len__(self):
        return len(self.file_list)

    def __getitem__(self, idx):
        data = self.file_list[idx]

        # 读取图片
        #img = cv2.imread(data['img'])
        image = cv2.imdecode(np.fromfile(data['img'], dtype=np.uint8), cv2.IMREAD_COLOR)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # 读取标签
        #label = cv2.imread(data['label'], cv2.IMREAD_GRAYSCALE)
        label = cv2.imdecode(np.fromfile(data['label'], dtype=np.uint8), cv2.IMREAD_GRAYSCALE)

        # 应用变换
        if self.transforms:
            img, label = self.transforms(img, label)

        return img, label


class SemanticSegmentationTrainer:
    """语义分割训练器"""

    def __init__(self, model_name: str, dataset_id: int, train_params: Dict[str, Any]):
        """
        初始化训练器

        Args:
            model_name: 模型名称
            dataset_id: 数据集ID
            train_params: 训练参数
        """
        self.model_name = model_name
        self.dataset_id = dataset_id
        self.train_params = train_params

        # 初始化数据库管理器
        self.db_manager = DatabaseManager()

        # 获取数据集信息
        self.dataset_info = self.db_manager.get_dataset(dataset_id)
        if not self.dataset_info:
            raise ValueError(f"无法获取数据集信息: {dataset_id}")

        # 设置输出目录
        self.output_base_dir = Path("D:/anylabeling2/AI_ENV/output/semantic_segmentation")
        self.model_output_path = self.output_base_dir / model_name
        self.model_output_path.mkdir(parents=True, exist_ok=True)

        # 创建PaddleSeg数据集目录
        self.paddleseg_dataset_path = self.model_output_path / "dataset"
        self.paddleseg_dataset_path.mkdir(exist_ok=True)

        # 设置日志
        log.add(self.model_output_path / 'train.log')
        get_paddle_logger()

        # 初始化模型
        self.model = None

        # 训练结果
        self.training_results = {}
        self.training_log = []
    
    def _log(self, message: str):
        """记录日志"""
        log.info(message)
        self.training_log.append(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}")

    def _get_dataset_annotations(self) -> List[Dict[str, Any]]:
        """获取数据集的所有标注信息"""
        try:
            annotations = self.db_manager.get_annotations_for_dataset(self.dataset_id)
            self._log(f"获取到 {len(annotations)} 个标注记录")
            return annotations
        except Exception as e:
            raise Exception(f"获取数据集标注失败: {e}")

    def _extract_labels_from_annotations(self, annotations: List[Dict[str, Any]]) -> Dict[str, int]:
        """从标注文件中提取所有标签"""
        labels = set()
        dataset_dir = self.dataset_info['output_dir']
        labels_dir = os.path.join(dataset_dir, 'labels')

        for annotation in annotations:
            image_path = annotation['image_path']
            label_file = os.path.splitext(os.path.basename(image_path))[0] + ".json"
            label_path = os.path.join(labels_dir, label_file)

            if os.path.exists(label_path):
                try:
                    with open(label_path, 'r', encoding='utf-8') as f:
                        label_data = json.load(f)

                    for shape in label_data.get('shapes', []):
                        labels.add(shape['label'])
                except Exception as e:
                    self._log(f"读取标注文件失败 {label_path}: {e}")

        # 添加背景类别
        labels.add('background')

        # 创建标签到索引的映射（背景为0）
        sorted_labels = sorted(labels)
        if 'background' in sorted_labels:
            sorted_labels.remove('background')
            sorted_labels.insert(0, 'background')

        label_to_idx = {label: idx for idx, label in enumerate(sorted_labels)}
        self._log(f"提取到标签: {list(label_to_idx.keys())}")
        return label_to_idx

    def _create_segmentation_mask(self, label_data: Dict, image_size: Tuple[int, int],
                                 label_to_idx: Dict[str, int]) -> np.ndarray:
        """创建语义分割掩码"""
        width, height = image_size
        mask = np.zeros((height, width), dtype=np.uint8)

        for shape in label_data.get('shapes', []):
            if shape['shape_type'] not in ['polygon', 'circle', 'rectangle']:
                continue

            label = shape['label']
            if label not in label_to_idx:
                continue

            class_id = label_to_idx[label]
            points = shape['points']

            if shape['shape_type'] == 'polygon':
                # 多边形填充
                pts = np.array(points, dtype=np.int32)
                cv2.fillPoly(mask, [pts], class_id)

            elif shape['shape_type'] == 'rectangle':
                # 矩形填充
                x1, y1 = points[0]
                x2, y2 = points[1]
                x1, x2 = int(min(x1, x2)), int(max(x1, x2))
                y1, y2 = int(min(y1, y2)), int(max(y1, y2))
                mask[y1:y2, x1:x2] = class_id

            elif shape['shape_type'] == 'circle':
                # 圆形填充
                (x1, y1), (x2, y2) = points
                center = (int(x1), int(y1))
                radius = int(np.sqrt((x2 - x1)**2 + (y2 - y1)**2))
                cv2.circle(mask, center, radius, class_id, -1)

        return mask

    def _prepare_paddleseg_dataset(self, annotations: List[Dict[str, Any]],
                                  label_to_idx: Dict[str, int]) -> Tuple[List[Dict], List[str]]:
        """准备PaddleSeg格式的数据集"""
        dataset_dir = self.dataset_info['output_dir']
        labels_dir = os.path.join(dataset_dir, 'labels')

        valid_samples = []
        failed_samples = []

        # 创建PaddleSeg数据集目录结构
        images_dir = self.paddleseg_dataset_path / 'images'
        annotations_dir = self.paddleseg_dataset_path / 'annotations'
        images_dir.mkdir(exist_ok=True)
        annotations_dir.mkdir(exist_ok=True)

        for annotation in annotations:
            image_path = annotation['image_path']
            if not os.path.exists(image_path):
                failed_samples.append(f"图片不存在: {image_path}")
                continue

            # 读取标注文件
            label_file = os.path.splitext(os.path.basename(image_path))[0] + ".json"
            label_path = os.path.join(labels_dir, label_file)

            if not os.path.exists(label_path):
                failed_samples.append(f"标注文件不存在: {label_path}")
                continue

            try:
                with open(label_path, 'r', encoding='utf-8') as f:
                    label_data = json.load(f)

                # 检查是否有有效的分割标注
                has_valid_annotation = False
                for shape in label_data.get('shapes', []):
                    if shape['shape_type'] in ['polygon', 'circle', 'rectangle'] and shape['label'] in label_to_idx:
                        has_valid_annotation = True
                        break

                if has_valid_annotation:
                    # 读取图片获取尺寸
                    #image = cv2.imread(image_path)
                    image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                    if image is None:
                        failed_samples.append(f"无法读取图片: {image_path}")
                        continue

                    height, width = image.shape[:2]

                    # 创建分割掩码
                    mask = self._create_segmentation_mask(label_data, (width, height), label_to_idx)

                    # 复制图片到PaddleSeg目录
                    image_name = os.path.basename(image_path)
                    dst_image_path = images_dir / image_name
                    shutil.copy2(image_path, dst_image_path)

                    # 保存掩码到PaddleSeg目录
                    mask_name = os.path.splitext(image_name)[0] + '.png'
                    dst_mask_path = annotations_dir / mask_name
                    cv2.imwrite(str(dst_mask_path), mask)

                    valid_samples.append({
                        'image_path': f"images/{image_name}",
                        'mask_path': f"annotations/{mask_name}"
                    })
                else:
                    failed_samples.append(f"无有效分割标注: {image_path}")

            except Exception as e:
                failed_samples.append(f"处理标注失败 {label_path}: {e}")

        self._log(f"有效样本: {len(valid_samples)}, 失败样本: {len(failed_samples)}")
        if failed_samples:
            self._log(f"失败样本详情: {failed_samples[:5]}...")  # 只显示前5个

        return valid_samples, failed_samples

    def _create_dataset_lists(self, valid_samples: List[Dict], train_ratio: float = 0.8):
        """创建PaddleSeg数据集列表文件"""
        random.shuffle(valid_samples)

        train_size = int(len(valid_samples) * train_ratio)
        train_samples = valid_samples[:train_size]
        val_samples = valid_samples[train_size:]

        self._log(f"数据集分割: 训练集 {len(train_samples)}, 验证集 {len(val_samples)}")

        # 创建训练集列表
        train_list_path = self.paddleseg_dataset_path / 'train_list.txt'
        with open(train_list_path, 'w') as f:
            for sample in train_samples:
                f.write(f"{sample['image_path']} {sample['mask_path']}\n")

        # 创建验证集列表
        val_list_path = self.paddleseg_dataset_path / 'val_list.txt'
        with open(val_list_path, 'w') as f:
            for sample in val_samples:
                f.write(f"{sample['image_path']} {sample['mask_path']}\n")

        return len(train_samples), len(val_samples)

    def _create_paddleseg_config(self, num_classes: int, train_samples: int, val_samples: int) -> str:
        """创建PaddleSeg配置文件"""

        # 获取训练参数
        batch_size = self.train_params.get('batch_size', 8)
        learning_rate = self.train_params.get('learning_rate', 0.01)
        epochs = self.train_params.get('epochs', 100)
        image_size = self.train_params.get('image_size', 512)

        # 确保路径使用正斜杠
        dataset_root = str(self.paddleseg_dataset_path).replace('\\', '/')
        train_list_path = str(self.paddleseg_dataset_path / 'train_list.txt').replace('\\', '/')
        val_list_path = str(self.paddleseg_dataset_path / 'val_list.txt').replace('\\', '/')

        config = {
            'batch_size': batch_size,
            'iters': epochs * (train_samples // batch_size),
            'train_dataset': {
                'type': 'Dataset',
                'dataset_root': dataset_root,
                'train_path': train_list_path,
                'num_classes': num_classes,
                'transforms': [
                    {'type': 'ResizeStepScaling', 'min_scale_factor': 0.5, 'max_scale_factor': 2.0, 'scale_step_size': 0.25},
                    {'type': 'RandomPaddingCrop', 'crop_size': [image_size, image_size]},
                    {'type': 'RandomHorizontalFlip'},
                    {'type': 'RandomDistort',"brightness_range": 0.5, "contrast_range": 0.5,"hue_range":0.5,"saturation_range": 0.5},
                    {'type': 'Normalize'}
                ],
                'mode': 'train'
            },
            'val_dataset': {
                'type': 'Dataset',
                'dataset_root': dataset_root,
                'val_path': val_list_path,
                'num_classes': num_classes,
                'transforms': [
                    {'type': 'Normalize'}
                ],
                'mode': 'val'
            },
            'optimizer': {
                'type': 'sgd',
                'momentum': 0.9,
                'weight_decay': 5.0e-4
            },
            'lr_scheduler': {
                'type': 'PolynomialDecay',
                'learning_rate': learning_rate,
                'end_lr': 0,
                'power': 0.9,
                'warmup_iters': 200,
                'warmup_start_lr': 1.0e-5
            },
            'model': {
                'type': 'PPLiteSeg',
                'backbone': {
                    'type': 'STDC2',
                    'pretrained': 'https://bj.bcebos.com/paddleseg/dygraph/PP_STDCNet2.tar.gz'
                },
                'num_classes': num_classes,
                'pretrained': None
            },
            'loss': {
                'types': [
                    {'type': 'OhemCrossEntropyLoss'},
                    {'type': 'OhemCrossEntropyLoss'},
                    {'type': 'OhemCrossEntropyLoss'}
                ],
                'coef': [1, 1, 1]
            }
        }

        config_path = self.model_output_path / 'paddleseg_config.yml'
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

        self._log(f"PaddleSeg配置文件已创建: {config_path}")
        return str(config_path)

    def _train_model(self, config_path: str, num_classes: int) -> Dict[str, Any]:
        """使用PaddleSeg训练模型"""
        try:
            self._log("开始PaddleSeg语义分割模型训练...")

            # 设置环境变量
            os.environ['CUDA_VISIBLE_DEVICES'] = '0' if paddle.is_compiled_with_cuda() else ''

            # 加载配置
            cfg = Config(config_path)
            builder = SegBuilder(cfg)

            # 创建模型
            model = builder.model

            # 创建数据集
            train_dataset = builder.train_dataset
            val_dataset = builder.val_dataset if hasattr(builder, 'val_dataset') else None

            # 创建优化器
            optimizer = builder.optimizer

            # 创建学习率调度器
            lr_scheduler = builder.lr_scheduler

            # 创建损失函数
            losses = builder.loss

            self._log(f"模型: {model.__class__.__name__}")
            self._log(f"训练样本数: {len(train_dataset)}")
            if val_dataset:
                self._log(f"验证样本数: {len(val_dataset)}")

            # 开始训练
            train(
                model=model,
                train_dataset=train_dataset,
                val_dataset=val_dataset,
                optimizer=optimizer,
                save_dir=str(self.model_output_path),
                iters=cfg.iters,
                batch_size=cfg.batch_size,
                resume_model=None,
                save_interval=cfg.dic.get('save_interval', 1000),
                log_iters=cfg.dic.get('log_iters', 10),
                num_workers=cfg.dic.get('num_workers', 0),
                use_vdl=cfg.dic.get('use_vdl', False),
                losses=losses,
                keep_checkpoint_max=cfg.dic.get('keep_checkpoint_max', 5),
                test_config=None,
                precision=cfg.dic.get('precision', 'fp32'),
                profiler_options=cfg.dic.get('profiler_options', None),
                to_static_training=cfg.dic.get('to_static_training', False)
            )

            # 获取训练指标
            best_model_path = self.model_output_path / 'best_model'
            if os.path.exists(best_model_path):
                self._log(f"最佳模型已保存: {best_model_path}")

            # 评估模型
            metrics = {}
            if val_dataset:
                try:
                    mean_iou, acc, _, _ = evaluate.evaluate(
                        model=model,
                        eval_dataset=val_dataset,
                        aug_eval=False,
                        scales=1.0,
                        flip_horizontal=False,
                        flip_vertical=False,
                        is_slide=False,
                        stride=None,
                        crop_size=None,
                        num_workers=0,
                        print_detail=True,
                        auc_roc=False
                    )

                    metrics = {
                        'mean_iou': float(mean_iou),
                        'pixel_accuracy': float(acc),
                        'final_iters': cfg.iters
                    }

                    self._log(f"验证指标 - mIoU: {mean_iou:.4f}, 像素准确率: {acc:.4f}")

                except Exception as e:
                    self._log(f"模型评估失败: {e}")
                    metrics = {
                        'mean_iou': 0.0,
                        'pixel_accuracy': 0.0,
                        'final_iters': cfg.iters
                    }

            self._log("PaddleSeg语义分割模型训练完成")
            return metrics

        except Exception as e:
            self._log(f"模型训练失败: {e}")
            raise

    def _export_to_onnx(self, num_classes: int) -> str:
        """导出模型为ONNX格式"""
        try:
            self._log("开始导出ONNX模型...")

            # 查找最佳模型
            best_model_dir = self.model_output_path / 'best_model'
            if not best_model_dir.exists():
                # 尝试查找其他模型文件
                model_files = list(self.model_output_path.glob('model_*.pdparams'))
                if model_files:
                    # 使用最新的模型文件
                    latest_model = max(model_files, key=os.path.getctime)
                    model_path = str(latest_model).replace('.pdparams', '')
                else:
                    raise FileNotFoundError("找不到训练好的模型文件")
            else:
                model_path = str(best_model_dir / 'model')

            self._log(f"加载模型: {model_path}")

            # 设置输入形状
            image_size = self.train_params.get('image_size', 512)
            input_shape = [1, 3, image_size, image_size]

            # 导出ONNX
            onnx_path = self.model_output_path / f"{self.model_name}.onnx"

            paddle2onnx.command.c_paddle_to_onnx(
                model_file=f"{model_path}.pdmodel",
                params_file=f"{model_path}.pdiparams",
                save_file=str(onnx_path),
                opset_version=11,
                enable_onnx_checker=True,
                input_shape_dict={'x': input_shape}
            )

            self._log(f"ONNX模型已导出: {onnx_path}")
            return str(onnx_path)

        except Exception as e:
            self._log(f"ONNX导出失败: {e}")
            raise

    def _save_training_results(self, metrics: Dict[str, Any], onnx_path: str,
                              label_to_idx: Dict[str, int]) -> Dict[str, Any]:
        """保存训练结果"""
        try:
            # 获取模型文件路径
            best_model_dir = self.model_output_path / 'best_model'
            model_path = best_model_dir if best_model_dir.exists() else self.model_output_path

            # 保存训练配置
            training_config = {
                'model_name': self.model_name,
                'dataset_id': self.dataset_id,
                'training_params': self.train_params,
                'training_time': datetime.now().isoformat(),
                'model_architecture': 'PaddleSeg-PPLiteSeg',
                'task_type': 'semantic_segmentation',
                'label_mapping': label_to_idx
            }

            config_file = self.model_output_path / 'training_config.json'
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(training_config, f, ensure_ascii=False, indent=2)

            # 保存训练指标
            metrics_file = self.model_output_path / 'training_metrics.json'
            with open(metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, ensure_ascii=False, indent=2)

            # 保存训练日志
            log_file = self.model_output_path / 'training_log.txt'
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.training_log))

            self._log("训练结果已保存")
            self._log(f"模型文件: {model_path}")
            self._log(f"ONNX文件: {onnx_path}")
            self._log(f"配置文件: {config_file}")
            self._log(f"指标文件: {metrics_file}")
            self._log(f"日志文件: {log_file}")

            return {
                'model_path': str(model_path),
                'onnx_path': onnx_path,
                'config_path': str(config_file),
                'metrics_path': str(metrics_file),
                'log_path': str(log_file),
                'metrics': metrics
            }

        except Exception as e:
            self._log(f"保存训练结果失败: {e}")
            raise

    def train(self) -> Dict[str, Any]:
        """执行完整的训练流程"""
        try:
            self._log(f"开始PaddleSeg语义分割模型训练")
            self._log(f"模型名称: {self.model_name}")
            self._log(f"数据集ID: {self.dataset_id}")
            self._log(f"输出目录: {self.model_output_path}")

            # 1. 获取数据集标注
            annotations = self._get_dataset_annotations()
            if not annotations:
                raise Exception("数据集中没有标注数据")

            # 2. 提取标签
            label_to_idx = self._extract_labels_from_annotations(annotations)
            if not label_to_idx:
                raise Exception("数据集中没有有效标签")

            num_classes = len(label_to_idx)

            # 3. 准备PaddleSeg格式数据集
            valid_samples, failed_samples = self._prepare_paddleseg_dataset(annotations, label_to_idx)
            if not valid_samples:
                raise Exception("没有有效的训练样本")

            # 4. 创建数据集列表文件
            train_ratio = self.train_params.get('train_ratio', 0.8)
            train_samples, val_samples = self._create_dataset_lists(valid_samples, train_ratio)

            # 5. 创建PaddleSeg配置文件
            config_path = self._create_paddleseg_config(num_classes, train_samples, val_samples)

            # 6. 训练模型
            metrics = self._train_model(config_path, num_classes)

            # 7. 导出ONNX
            onnx_path = self._export_to_onnx(num_classes)

            # 8. 保存结果
            results = self._save_training_results(metrics, onnx_path, label_to_idx)

            # 输出最终结果
            final_output = {
                'status': 'success',
                'model_path': results['model_path'],
                'onnx_path': results['onnx_path'],
                'metrics': results['metrics'],
                'train_samples': train_samples,
                'val_samples': val_samples,
                'failed_samples': len(failed_samples),
                'num_classes': num_classes,
                'message': 'PaddleSeg语义分割训练和ONNX导出完成'
            }

            self._log("训练流程完成！")
            return final_output

        except Exception as e:
            self._log(f"训练流程失败: {e}")

            # 输出错误结果
            error_output = {
                'status': 'error',
                'error_message': str(e),
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': 'PaddleSeg语义分割训练失败'
            }
            return error_output


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练脚本')
    
    # 支持两种参数格式
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--config', type=str, help='配置文件路径')
    group.add_argument('--model_name', type=str, help='模型名称 (与--dataset_id和--train_params一起使用)')
    
    parser.add_argument('--dataset_id', type=int, help='数据集ID')
    parser.add_argument('--train_params', type=str, help='训练参数JSON字符串')

    args = parser.parse_args()

    # 根据参数格式解析配置
    if args.config:
        # 从配置文件读取参数
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            model_name = config.get('model_name', '')
            dataset_id = config.get('dataset_id', 0)
            train_params = config.get('train_params', {})
            
            if not model_name:
                error_output = {
                    'status': 'error',
                    'error_message': '配置文件中缺少model_name',
                    'model_path': '',
                    'onnx_path': '',
                    'metrics': {},
                    'message': '配置文件格式错误'
                }
                print(json.dumps(error_output, ensure_ascii=False))
                return False
                
            if not dataset_id:
                error_output = {
                    'status': 'error',
                    'error_message': '配置文件中缺少dataset_id',
                    'model_path': '',
                    'onnx_path': '',
                    'metrics': {},
                    'message': '配置文件格式错误'
                }
                print(json.dumps(error_output, ensure_ascii=False))
                return False
                
        except FileNotFoundError:
            error_output = {
                'status': 'error',
                'error_message': f'配置文件不存在: {args.config}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '配置文件不存在'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False
        except json.JSONDecodeError as e:
            error_output = {
                'status': 'error',
                'error_message': f'配置文件JSON解析失败: {e}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '配置文件格式错误'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False
        except Exception as e:
            error_output = {
                'status': 'error',
                'error_message': f'读取配置文件失败: {e}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '配置文件读取失败'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False
    else:
        # 从命令行参数读取
        if not args.dataset_id or not args.train_params:
            error_output = {
                'status': 'error',
                'error_message': '使用--model_name时必须同时提供--dataset_id和--train_params',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '参数不完整'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False

        model_name = args.model_name
        dataset_id = args.dataset_id
        
        try:
            train_params = json.loads(args.train_params)
        except json.JSONDecodeError as e:
            error_output = {
                'status': 'error',
                'error_message': f'训练参数JSON解析失败: {e}',
                'model_path': '',
                'onnx_path': '',
                'metrics': {},
                'message': '参数解析失败'
            }
            print(json.dumps(error_output, ensure_ascii=False))
            return False

    # 创建训练器并开始训练
    try:
        trainer = SemanticSegmentationTrainer(
            model_name=model_name,
            dataset_id=dataset_id,
            train_params=train_params
        )

        result = trainer.train()
        print(json.dumps(result, ensure_ascii=False))

        return result['status'] == 'success'

    except Exception as e:
        print(f"[ERROR] 训练器初始化失败: {e}")
        traceback.print_exc()

        # 输出错误结果到stdout
        error_output = {
            'status': 'error',
            'error_message': str(e),
            'model_path': '',
            'onnx_path': '',
            'metrics': {},
            'message': '训练器初始化失败'
        }
        print(json.dumps(error_output, ensure_ascii=False))

        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
