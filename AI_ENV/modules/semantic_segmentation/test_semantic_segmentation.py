"""
语义分割测试脚本
基于PaddleSeg的语义分割模型测试和评估
"""
import argparse
import json
import os
import sys
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
anylabeling_root = project_root / "anylabeling"
sys.path.insert(0, str(anylabeling_root))

try:
    import paddle
    import paddleseg
    from paddleseg import utils
    from paddleseg.core import predict
    from paddleseg.models import *
    import cv2
    import numpy as np
    from PIL import Image
    import matplotlib.pyplot as plt
    from sklearn.metrics import confusion_matrix
except ImportError as e:
    print(f"[ERROR] 缺少必要的依赖包: {e}")
    print("[ERROR] 请安装: pip install paddlepaddle paddleseg opencv-python pillow matplotlib scikit-learn")
    sys.exit(1)


class SemanticSegmentationTester:
    """语义分割测试器"""
    
    def __init__(self, model_path: str, config_path: str, test_params: Dict[str, Any], output_path: str = None):
        """
        初始化测试器
        
        Args:
            model_path: 模型文件路径
            config_path: 配置文件路径
            test_params: 测试参数
            output_path: 输出路径（可选，默认为模型目录下的test_results）
        """
        self.model_path = model_path
        self.config_path = config_path
        self.test_params = test_params
        
        # 设置输出目录
        if output_path:
            self.output_path = Path(output_path)
        else:
            model_dir = Path(model_path).parent
            self.output_path = model_dir / "test_results"
        
        self.output_path.mkdir(exist_ok=True)
        
        # 加载模型和配置
        self.model = None
        self.cfg = None
        self._load_model()
        
        # 测试结果
        self.test_results = {}
        self.test_log = []
    
    def _log(self, message: str):
        """记录日志"""
        print(f"[INFO] {message}")
        self.test_log.append(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}")
    
    def _load_model(self):
        """加载PaddleSeg模型"""
        try:
            self._log(f"加载PaddleSeg语义分割模型: {self.model_path}")
            
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
            
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
            # 加载配置
            self.cfg = utils.Config(self.config_path)
            
            # 创建模型
            self.model = self.cfg.model
            
            # 加载权重
            if os.path.isdir(self.model_path):
                # 目录格式
                model_file = os.path.join(self.model_path, 'model.pdparams')
            else:
                # 文件格式
                model_file = self.model_path
            
            if os.path.exists(model_file):
                param_state_dict = paddle.load(model_file)
                self.model.set_dict(param_state_dict)
                self.model.eval()
            
            self._log(f"模型加载成功")
            
        except Exception as e:
            self._log(f"模型加载失败: {e}")
            raise
    
    def _preprocess_image(self, image_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """预处理图片"""
        try:
            # 读取图片
            #image = cv2.imread(image_path)
            image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
            if image is None:
                raise ValueError(f"无法读取图片: {image_path}")
            
            # 转换为RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            original_image = image.copy()
            
            # 应用预处理变换
            if hasattr(self.cfg, 'val_dataset') and hasattr(self.cfg.val_dataset, 'transforms'):
                transforms = self.cfg.val_dataset.transforms
                for transform in transforms:
                    if hasattr(transform, 'apply_image'):
                        image = transform.apply_image(image)
            
            return image, original_image
            
        except Exception as e:
            self._log(f"图片预处理失败 {image_path}: {e}")
            raise
    
    def _run_inference(self, image: np.ndarray) -> np.ndarray:
        """运行推理"""
        try:
            # 转换为Paddle张量
            if len(image.shape) == 3:
                image = np.transpose(image, (2, 0, 1))  # HWC -> CHW
            
            image = np.expand_dims(image, axis=0)  # 添加batch维度
            image_tensor = paddle.to_tensor(image, dtype='float32')
            
            # 运行推理
            with paddle.no_grad():
                pred = self.model(image_tensor)
                if isinstance(pred, (list, tuple)):
                    pred = pred[0]
                
                # 获取预测结果
                pred = paddle.argmax(pred, axis=1)
                pred = pred.numpy().squeeze()
            
            return pred
            
        except Exception as e:
            self._log(f"推理失败: {e}")
            raise
    
    def _calculate_metrics(self, pred_mask: np.ndarray, gt_mask: np.ndarray, num_classes: int) -> Dict[str, float]:
        """计算评估指标"""
        try:
            # 确保掩码尺寸一致
            if pred_mask.shape != gt_mask.shape:
                pred_mask = cv2.resize(pred_mask, (gt_mask.shape[1], gt_mask.shape[0]), 
                                     interpolation=cv2.INTER_NEAREST)
            
            # 计算混淆矩阵
            valid_mask = (gt_mask >= 0) & (gt_mask < num_classes)
            pred_valid = pred_mask[valid_mask]
            gt_valid = gt_mask[valid_mask]
            
            # 像素准确率
            pixel_accuracy = np.mean(pred_valid == gt_valid)
            
            # 计算每个类别的IoU
            ious = []
            for class_id in range(num_classes):
                pred_class = (pred_valid == class_id)
                gt_class = (gt_valid == class_id)
                
                intersection = np.logical_and(pred_class, gt_class).sum()
                union = np.logical_or(pred_class, gt_class).sum()
                
                if union > 0:
                    iou = intersection / union
                    ious.append(iou)
                else:
                    ious.append(float('nan'))
            
            # 计算mIoU（忽略NaN值）
            valid_ious = [iou for iou in ious if not np.isnan(iou)]
            mean_iou = np.mean(valid_ious) if valid_ious else 0.0
            
            return {
                'pixel_accuracy': pixel_accuracy,
                'mean_iou': mean_iou,
                'class_ious': ious,
                'valid_classes': len(valid_ious)
            }
            
        except Exception as e:
            self._log(f"指标计算失败: {e}")
            return {
                'pixel_accuracy': 0.0,
                'mean_iou': 0.0,
                'class_ious': [],
                'valid_classes': 0
            }
    
    def _visualize_results(self, original_image: np.ndarray, pred_mask: np.ndarray, 
                          gt_mask: np.ndarray, save_path: str, class_names: List[str] = None):
        """可视化结果"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 12))
            
            # 原始图片
            axes[0, 0].imshow(original_image)
            axes[0, 0].set_title('Original Image')
            axes[0, 0].axis('off')
            
            # 预测掩码
            axes[0, 1].imshow(pred_mask, cmap='tab20')
            axes[0, 1].set_title('Predicted Mask')
            axes[0, 1].axis('off')
            
            # 真实掩码
            if gt_mask is not None:
                axes[1, 0].imshow(gt_mask, cmap='tab20')
                axes[1, 0].set_title('Ground Truth Mask')
                axes[1, 0].axis('off')
                
                # 叠加显示
                overlay = original_image.copy()
                # 简单的叠加显示
                colored_pred = plt.cm.tab20(pred_mask / pred_mask.max() if pred_mask.max() > 0 else 1)[:, :, :3]
                overlay = 0.7 * overlay + 0.3 * (colored_pred * 255)
                overlay = np.clip(overlay, 0, 255).astype(np.uint8)
                
                axes[1, 1].imshow(overlay)
                axes[1, 1].set_title('Overlay')
                axes[1, 1].axis('off')
            else:
                # 如果没有真实掩码，显示叠加结果
                overlay = original_image.copy()
                colored_pred = plt.cm.tab20(pred_mask / pred_mask.max() if pred_mask.max() > 0 else 1)[:, :, :3]
                overlay = 0.7 * overlay + 0.3 * (colored_pred * 255)
                overlay = np.clip(overlay, 0, 255).astype(np.uint8)
                
                axes[1, 0].imshow(overlay)
                axes[1, 0].set_title('Overlay')
                axes[1, 0].axis('off')
                
                axes[1, 1].axis('off')
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            self._log(f"可视化结果已保存: {save_path}")
            
        except Exception as e:
            self._log(f"可视化失败: {e}")
    
    def test_on_images(self, test_images: List[str], gt_masks: List[str] = None, 
                      class_names: List[str] = None) -> Dict[str, Any]:
        """在图片列表上进行测试"""
        try:
            self._log(f"开始测试，共 {len(test_images)} 张图片")
            
            all_metrics = []
            results = []
            num_classes = getattr(self.cfg.model, 'num_classes', 2)
            
            for i, image_path in enumerate(test_images):
                self._log(f"处理图片 {i+1}/{len(test_images)}: {os.path.basename(image_path)}")
                
                try:
                    # 预处理图片
                    processed_image, original_image = self._preprocess_image(image_path)
                    
                    # 运行推理
                    pred_mask = self._run_inference(processed_image)
                    
                    # 调整预测掩码尺寸到原始图片尺寸
                    if pred_mask.shape != original_image.shape[:2]:
                        pred_mask = cv2.resize(pred_mask, 
                                             (original_image.shape[1], original_image.shape[0]), 
                                             interpolation=cv2.INTER_NEAREST)
                    
                    # 保存预测结果
                    image_name = os.path.splitext(os.path.basename(image_path))[0]
                    pred_save_path = self.output_path / f"{image_name}_pred_mask.png"
                    cv2.imwrite(str(pred_save_path), pred_mask.astype(np.uint8))
                    
                    result = {
                        'image_path': image_path,
                        'pred_mask_path': str(pred_save_path),
                        'unique_classes': len(np.unique(pred_mask))
                    }
                    
                    # 如果有真实标签，计算指标
                    gt_mask = None
                    if gt_masks and i < len(gt_masks) and gt_masks[i]:
                        #gt_mask = cv2.imread(gt_masks[i], cv2.IMREAD_GRAYSCALE)
                        gt_mask = cv2.imdecode(np.fromfile(gt_masks[i], dtype=np.uint8), cv2.IMREAD_GRAYSCALE)
                        if gt_mask is not None:
                            metrics = self._calculate_metrics(pred_mask, gt_mask, num_classes)
                            result.update(metrics)
                            all_metrics.append(metrics)
                    
                    results.append(result)
                    
                    # 可视化结果
                    vis_save_path = self.output_path / f"{image_name}_visualization.png"
                    self._visualize_results(original_image, pred_mask, gt_mask, 
                                          str(vis_save_path), class_names)
                    
                except Exception as e:
                    self._log(f"处理图片失败 {image_path}: {e}")
                    continue
            
            # 计算平均指标
            avg_metrics = {}
            if all_metrics:
                avg_metrics = {
                    'avg_pixel_accuracy': np.mean([m['pixel_accuracy'] for m in all_metrics]),
                    'avg_mean_iou': np.mean([m['mean_iou'] for m in all_metrics]),
                    'avg_valid_classes': np.mean([m['valid_classes'] for m in all_metrics])
                }
            
            test_summary = {
                'total_images': len(test_images),
                'processed_images': len(results),
                'failed_images': len(test_images) - len(results),
                'num_classes': num_classes,
                'average_metrics': avg_metrics,
                'detailed_results': results
            }
            
            self._log("测试完成")
            return test_summary
            
        except Exception as e:
            self._log(f"测试失败: {e}")
            raise
    
    def save_test_results(self, test_summary: Dict[str, Any]):
        """保存测试结果"""
        try:
            # 保存测试结果
            results_file = self.output_path / 'test_results.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(test_summary, f, ensure_ascii=False, indent=2)
            
            # 保存测试日志
            log_file = self.output_path / 'test_log.txt'
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.test_log))
            
            self._log(f"测试结果已保存: {results_file}")
            self._log(f"测试日志已保存: {log_file}")
            
        except Exception as e:
            self._log(f"保存测试结果失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='语义分割测试脚本')
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--config_path', type=str, required=True, help='配置文件路径')
    parser.add_argument('--test_images', type=str, required=True, help='测试图片目录或文件列表')
    parser.add_argument('--output_path', type=str, help='结果输出路径（可选）')
    parser.add_argument('--gt_masks', type=str, help='真实掩码目录或文件列表（可选）')
    parser.add_argument('--class_names', type=str, help='类别名称文件（可选）')
    parser.add_argument('--test_params', type=str, default='{}', help='测试参数JSON字符串')
    
    args = parser.parse_args()
    
    # 解析测试参数
    try:
        test_params = json.loads(args.test_params)
    except json.JSONDecodeError as e:
        print(f"[ERROR] 测试参数JSON解析失败: {e}")
        return False
    
    # 获取测试图片列表
    test_images = []
    if os.path.isdir(args.test_images):
        # 目录
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.tif', '*.tiff']:
            test_images.extend(Path(args.test_images).glob(ext))
        test_images = [str(p) for p in test_images]
    else:
        # 文件列表
        with open(args.test_images, 'r') as f:
            test_images = [line.strip() for line in f if line.strip()]
    
    # 获取真实掩码列表
    gt_masks = None
    if args.gt_masks:
        if os.path.isdir(args.gt_masks):
            # 目录
            gt_masks = []
            for img_path in test_images:
                img_name = os.path.splitext(os.path.basename(img_path))[0]
                mask_path = os.path.join(args.gt_masks, f"{img_name}_mask.png")
                if os.path.exists(mask_path):
                    gt_masks.append(mask_path)
                else:
                    gt_masks.append(None)
        else:
            # 文件列表
            with open(args.gt_masks, 'r') as f:
                gt_masks = [line.strip() for line in f if line.strip()]
    
    # 获取类别名称
    class_names = None
    if args.class_names and os.path.exists(args.class_names):
        with open(args.class_names, 'r') as f:
            class_names = [line.strip() for line in f if line.strip()]
    
    # 创建测试器并开始测试
    try:
        tester = SemanticSegmentationTester(
            model_path=args.model_path,
            config_path=args.config_path,
            test_params=test_params,
            output_path=args.output_path
        )
        
        test_summary = tester.test_on_images(test_images, gt_masks, class_names)
        tester.save_test_results(test_summary)
        
        print(json.dumps(test_summary, ensure_ascii=False, indent=2))
        return True
        
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
