#!/usr/bin/env python3
"""测试配置文件解析"""

import json
import sys

def test_config_parsing():
    config_file = "AI_ENV/temp/train_config_semantic_segmentation_20250814_115334_20250814_120045.json"
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"Config loaded: {config}")
        
        model_name = config.get('model_name', '')
        dataset_id = config.get('dataset_id', 0)
        train_params = config.get('train_params', {})
        
        print(f"model_name: {model_name} (type: {type(model_name)})")
        print(f"dataset_id: {dataset_id} (type: {type(dataset_id)})")
        print(f"train_params: {train_params} (type: {type(train_params)})")
        
        print(f"not model_name: {not model_name}")
        print(f"not dataset_id: {not dataset_id}")
        
        if not model_name:
            print("ERROR: model_name is empty")
            return False
            
        if not dataset_id:
            print("ERROR: dataset_id is empty/zero")
            return False
            
        print("All parameters are valid")
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    test_config_parsing()