#!/usr/bin/env python3
"""检查数据集18是否存在"""

from anylabeling.utils.db_manager import DatabaseManager

def main():
    db = DatabaseManager()
    
    # 检查特定数据集
    dataset = db.get_dataset(18)
    print(f"Dataset 18: {dataset}")
    
    # 列出所有数据集
    all_datasets = db.get_all_datasets()
    print(f"\nAll datasets ({len(all_datasets)} total):")
    for ds in all_datasets:
        print(f"  ID: {ds.get('id')}, Name: {ds.get('name')}, Path: {ds.get('path')}")

if __name__ == "__main__":
    main()