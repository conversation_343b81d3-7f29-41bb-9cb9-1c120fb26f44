#!/usr/bin/env python3
"""测试修复后的数据库管理器"""

from anylabeling.utils.db_manager import DatabaseManager

def main():
    try:
        # 创建数据库管理器实例
        db = DatabaseManager()
        print(f"数据库路径: {db.db_path}")
        
        # 获取所有数据集
        datasets = db.get_all_datasets()
        print(f"数据集数量: {len(datasets)}")
        
        for dataset in datasets:
            print(f"ID: {dataset.get('id')}, Name: {dataset.get('name')}, Path: {dataset.get('path')}")
        
        # 检查数据集18是否存在
        dataset_18 = db.get_dataset(18)
        if dataset_18:
            print(f"\n数据集18存在: {dataset_18.get('name')} - {dataset_18.get('path')}")
        else:
            print("\n数据集18不存在")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
